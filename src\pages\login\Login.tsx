import { useState } from "react";
import { useAuth } from "../../hooks/useAuth";
import { useNavigate, useSearch } from "@tanstack/react-router";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const { login, isLoggingIn } = useAuth();

  const navigate = useNavigate();
  const search = useSearch({ strict: false });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isLoggingIn) {
      return;
    }

    setError("");

    try {
      // Use mutateAsync instead of mutate to get a Promise
      await new Promise((resolve, reject) => {
        login(
          { email, password },
          {
            onSuccess: (data) => {
              resolve(data);
            },
            onError: (error) => {
              reject(error);
            },
          }
        );
      });

      // Now navigate after successful login
      const redirectPath = (search as { redirect?: string }).redirect || "/";
      navigate({ to: redirectPath });
    } catch (err) {
      setError("Invalid email or password. Please try again.");
      console.error("Login error:", err);
    }
  };

  return (
    <section className="grid grid-cols-2 gap-x-20 pt-32 relative">
      <section className="h-64 flex items-center justify-center">
        <div className="max-w-xl rounded text-white">
          <img src="/brandLogo.svg" alt="brand-logo" />
          <h1 className="font-semibold mb-2 text-4xl mt-6">Sign in to</h1>
          <p className="text-lg font-light my-4">
            Streamline your company's expense management <br /> in one secure
            platform.
          </p>
          <p className="text-sm font-light">
            Sign in to get started and take control of your <br /> expenses
            today.
          </p>
        </div>
      </section>

      <section className="z-20 mx-auto bg-white rounded-2xl p-12 shadow-lg absolute right-100 top-4/10">
        <h3 className="text-xl font-normal mb-2">
          Welcome to{" "}
          <span className="text-[#0ee6c9] font-semibold">EXPENSO</span>
        </h3>
        <h1 className="text-6xl font-semibold mb-12">Sign in</h1>

        <form className="grid gap-8 w-110" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
              {error}
            </div>
          )}

          <div className="grid gap-2">
            <label
              htmlFor="email"
              className="text-sm font-normal text-gray-800"
            >
              Enter your email address
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
              required
              disabled={isLoggingIn}
            />
          </div>

          <div className="grid gap-2">
            <label
              htmlFor="password"
              className="text-sm font-normal text-gray-700"
            >
              Enter your Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="employee123"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
              required
              disabled={isLoggingIn}
            />
          </div>

          <button
            type="submit"
            disabled={isLoggingIn}
            className="brand-gradient rounded-lg text-white py-3 mt-4 font-semibold hover:opacity-90 transition disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isLoggingIn ? "Signing in..." : "Sign in"}
          </button>

          <div className="mt-4 space-y-2">
            <p className="text-sm text-gray-500">Try these test accounts:</p>
            <button
              type="button"
              onClick={() => {
                setEmail("<EMAIL>");
                setPassword("employee123");
              }}
              disabled={isLoggingIn}
              className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200 disabled:opacity-50"
            >
              Employee
            </button>
            <button
              type="button"
              onClick={() => {
                setEmail("<EMAIL>");
                setPassword("manager123");
              }}
              disabled={isLoggingIn}
              className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200 ml-2 disabled:opacity-50"
            >
              Manager
            </button>
            <button
              type="button"
              onClick={() => {
                setEmail("<EMAIL>");
                setPassword("finance123");
              }}
              disabled={isLoggingIn}
              className="text-xs bg-gray-100 px-2 py-1 rounded hover:bg-gray-200 ml-2 disabled:opacity-50"
            >
              Finance
            </button>
          </div>
        </form>
      </section>
    </section>
  );
};

export default Login;
