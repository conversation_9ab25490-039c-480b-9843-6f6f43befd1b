import React, { useState } from "react";
import {
  FaChevronLeft,
  FaCheckCircle,
  FaTimesCircle,
  FaChevronDown,
  FaChevronUp,
  FaUser,
  FaCreditCard,
  FaRegCommentDots,
  FaClock,
} from "react-icons/fa";
import { FcCancel } from "react-icons/fc";
import { FiPlusCircle } from "react-icons/fi";
import { useNavigate } from "react-router-dom";
import infoGif from "../../components/icons/info.gif";
import successQuery from "../../components/icons/successQuery.gif";
// Types
interface SupportProps {
  onBack?: () => void;
}

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: "all" | "account" | "billing" | "prompting";
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: "success" | "error";
  title: string;
  message: string;
  icons?: string | null | undefined;
}

interface QueryItem {
  id: string;
  description: string;
  submissionDate: string;
  status: "Pending" | "Answered" | "Canceled";
}

// Mock data for My Queries
const mockQueries: QueryItem[] = [
  {
    id: "Query#458",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Pending",
  },
  {
    id: "Query#459",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Answered",
  },
  {
    id: "Query#460",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Answered",
  },
  {
    id: "Query#461",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Canceled",
  },
  {
    id: "Query#458",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Pending",
  },
  {
    id: "Query#459",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Answered",
  },
  {
    id: "Query#460",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Answered",
  },
  {
    id: "Query#461",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Canceled",
  },
];

// Modal Component
const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  type,
  title,
  message,
  icons,
}) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-md bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fadeIn"
      onClick={handleBackdropClick}
    >
      <div className="bg-white max-w-3xl mx-auto rounded-lg shadow-xl w-full transform animate-slideIn">
        <div className="flex justify-between">
          <div className="p-6 flex-1">
            <div className="items-center mb-4">
              {type === "success" ? (
                <img
                  src={icons ?? ""}
                  alt="Success"
                  className="w-[344px] h-[344px] object-contain m-5 mx-auto"
                />
              ) : (
                <FaTimesCircle className="text-red-500 w-6 h-6 mr-3" />
              )}
              <h1 className="text-lg font-semibold text-center text-gray-900">
                {title}
              </h1>
            </div>
            <p className="text-gray-600 mb-6 text-center">{message}</p>

            {type === "success" && (
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    onClose();
                    navigate("/profile/support");
                  }}
                  className="dark-accent  text-white rounded m-4 px-12 py-2 cursor-pointer transition-colors duration-200 hover:scale-105"
                >
                  Back to Support
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};


// My Queries Component
const MyQueries: React.FC = () => {
  return (
    <div className="mb-8 animate-slideUp" style={{ animationDelay: "0.2s" }}>
      <h2 className="text-2xl font-bold text-[#5D7285] mb-6">My Queries</h2>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Table Header */}
        <div className="bg-gray-50 px-4 md:px-6 py-4 border-b border-gray-200">
          <div className="grid grid-cols-4 gap-4 text-center text-sm font-medium text-gray-700">
            <div>Query ID</div>
            <div>Query Description</div>
            <div>Submission Date</div>
            <div>Query Status</div>
          </div>
        </div>

        {/* Table Body with Scroll */}
        <div className="max-h-[320px] overflow-y-auto divide-y divide-gray-200 scrollbar-hide">
          {mockQueries.map((query) => (
            <div
              key={query.id}
              className="px-4 md:px-6 py-4 hover:bg-gray-50 transition-colors text-center duration-200 animate-slideUp"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="grid grid-cols-4 gap-4 items-center">
                <div className="text-sm text-gray-900 font-medium">
                  {query.id}
                </div>
                <div className="text-sm text-[slategray] text-left">
                  {query.description}
                </div>
                <div className="text-sm text-[slategray]">
                  {query.submissionDate}
                </div>
                <div className="text-sm">
                  <span
                    className={`inline-flex items-center px-5 py-2 rounded-full text-xs font-medium ${
                      query.status === "Pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : query.status === "Answered"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {query.status === "Pending" && (
                      <FaClock className="w-3 h-3 mr-1" />
                    )}
                    {query.status === "Answered" && (
                      <FaCheckCircle className="w-3 h-3 mr-1" />
                    )}
                    {query.status === "Canceled" && (
                      <FcCancel className="w-3 h-3 mr-1" />
                    )}
                    {query.status}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
// FAQ Data
const faqData: FAQItem[] = [
  {
    id: 1,
    question: "Can I edit a generated response?",
    answer:
      "Yes, you can edit generated responses by clicking the edit button next to the response. You can modify the content and regenerate if needed.",
    category: "all",
  },
  {
    id: 2,
    question: "How do I reset my password?",
    answer:
      "You can reset your password by selecting 'Forgot password?' on the login screen. Follow the instructions sent to your email to create a new one. If you need further assistance, contact our support team.",
    category: "account",
  },
  {
    id: 3,
    question: "Can I export my data?",
    answer:
      "Yes, you can export your conversation history and data from your account settings. Go to Privacy & Data section and select 'Export Data'.",
    category: "all",
  },
  {
    id: 4,
    question: "How can I delete my history?",
    answer:
      "You can delete your conversation history by going to Settings > Privacy & Data > Delete History. You can choose to delete individual conversations or all history.",
    category: "all",
  },
  {
    id: 5,
    question: "Are my data secure?",
    answer:
      "Yes, we take data security seriously. All data is encrypted in transit and at rest. We follow industry-standard security practices and regularly audit our systems.",
    category: "all",
  },
  {
    id: 6,
    question: "How do I report an issue?",
    answer:
      "You can report issues through this support form, email us directly, or use the feedback button in the application. Please provide as much detail as possible.",
    category: "all",
  },
  {
    id: 7,
    question: "How do I update my billing information?",
    answer:
      "Go to Account Settings > Billing to update your payment method, billing address, or subscription details. Changes take effect immediately.",
    category: "billing",
  },
  {
    id: 8,
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards, PayPal, and bank transfers for enterprise accounts. All payments are processed securely through our payment partners.",
    category: "billing",
  },
  {
    id: 9,
    question: "How do I write better prompts?",
    answer:
      "For better prompts, be specific and clear about what you want. Provide context, examples, and specify the format you prefer. Break complex tasks into smaller steps.",
    category: "prompting",
  },
  {
    id: 10,
    question: "What are some prompting best practices?",
    answer:
      "Use clear instructions, provide examples, specify output format, use step-by-step reasoning, and iterate on your prompts based on the results you get.",
    category: "prompting",
  },
];

// FAQ Component
const FAQ: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<
    "all" | "account" | "billing" | "prompting"
  >("all");

  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  const toggleExpanded = (id: number) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const filteredFAQs = faqData.filter(
    (item) => activeCategory === "all" || item.category === activeCategory
  );

  const categories = [
    { id: "all", label: "All", icon: FaRegCommentDots },
    { id: "account", label: "Account", icon: FaUser },
    { id: "billing", label: "Billing", icon: FaCreditCard },
    { id: "prompting", label: "Prompting", icon: FaRegCommentDots },
  ];

  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold text-[slategray] mb-6">FAQ's</h2>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() =>
              setActiveCategory(
                id as "all" | "account" | "billing" | "prompting"
              )
            }
            className={`cursor-pointer w-70 sm:w-30 md:w-40 lg:w-50 text-center  flex items-center justify-center px-4 py-2 rounded-full transition-transform duration-300 hover:scale-105 ${
              activeCategory === id
                ? "bg-[darkturquoise] text-white shadow-lg"
                : "bg-[aliceblue] text-[darkturquoise] hover:bg-gray-200"
            }`}
          >
            <Icon className="w-4 h-4 mr-2" />
            {label}
          </button>
        ))}
      </div>

      {/* FAQ Items */}
      <div className="space-y-4 max-h-[320px] overflow-y-auto divide-y divide-gray-200 scrollbar-hide">
        {filteredFAQs.map((item) => (
          <div
            key={item.id}
            className=" border-0 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200"
          >
            <button
              onClick={() => toggleExpanded(item.id)}
              className="w-full px-6 py-4 cursor-pointer text-left flex items-center justify-between bg-[whitesmoke] hover:bg-[whitesmoke] transition-colors duration-200"
            >
              <span className="font-medium text-gray-900">{item.question}</span>
              {expandedItems.includes(item.id) ? (
                <FaChevronUp className="w-5 h-5 text-teal-500" />
              ) : (
                <FaChevronDown className="w-5 h-5 text-teal-500" />
              )}
            </button>

            <div
              className={`transition-all duration-300 ease-in-out transform origin-top ${
                expandedItems.includes(item.id)
                  ? "max-h-96 opacity-100"
                  : "max-h-0 opacity-0"
              } overflow-hidden`}
            >
              <div className="px-6 pb-4 bg-[whitesmoke]">
                <p className="text-[dimgray]">{item.answer}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Main Support Component
const Support: React.FC<SupportProps> = ({ onBack }) => {
  const [query, setQuery] = useState<string>("");
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [modal, setModal] = useState<{
    isOpen: boolean;
    type: "success" | "error";
    title: string;
    message: string;
    icons: string | null;
  }>({
    isOpen: false,
    type: "success",
    title: "",
    message: "",
    icons: "",
  });
  const navigate = useNavigate();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate("/profile");
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        setModal({
          isOpen: true,
          type: "error",
          title: "Invalid File Type",
          message: "Please upload an image file (JPG, PNG, GIF, etc.)",
          icons: null,
        });
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        setModal({
          isOpen: true,
          type: "error",
          title: "File Too Large",
          message: "Please upload an image smaller than 5MB",
          icons: null,
        });
        return;
      }

      setSelectedImage(file);
    }
  };

  const handleSubmit = async () => {
    if (!query.trim()) {
      setModal({
        isOpen: true,
        type: "error",
        title: "Query Required",
        message: "Please enter your query before submitting",
        icons: null,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Simulate random success/failure (80% success rate)
      const isSuccess = Math.random() > 0.2;

      if (isSuccess) {
        setModal({
          isOpen: true,
          type: "success",
          title: "Query Submitted",
          icons: successQuery,
          message:
            "You have successfully asked a query and should expect a response within 48 hours",
        });
        setQuery("");
        setSelectedImage(null);
      } else {
        setModal({
          isOpen: true,
          type: "error",
          title: "Submission Failed",
          icons: null,
          message:
            "We encountered an error while submitting your query. Please try again or contact support directly.",
        });
      }
    } catch (error) {
      console.error("Submission error:", error);
      setModal({
        isOpen: true,
        type: "error",
        title: "Network Error",
        message: "Please check your internet connection and try again.",
        icons: null,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const closeModal = () => {
    setModal((prev) => ({ ...prev, isOpen: false }));
  };

  return (
    <div className="min-h-screen  bg-grey-100 md:p-6 lg:p-8">
      <div className="max-w-5xl mx-auto bg-white shadow-lg rounded-lg p-5">
        {/* Header */}
        <div className="mb-8 animate-fadeIn">
          <button
            onClick={handleBack}
            className="flex items-center cursor-pointer text-teal-600 hover:text-teal-700 mb-4 transition-transform duration-300 hover:scale-110"
          >
            <FaChevronLeft className="w-5 h-5 mr-1 transition-transform duration-300 hover:scale-110" />
            Back
          </button>
          <h1 className="text-3xl font-bold text-[#5D7285] mb-2 ">Support</h1>
        </div>

        {/* Query Form */}
        <div className="  mb-8 animate-slideUp">
          <h2 className="text-xl font-semibold text-[#5D7285] mb-2">
            Raise a Query
          </h2>
          <p className="text-[#A4A4A4] mb-6">Ask your query</p>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg">
              <textarea
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Enter Details"
                className="w-full min-h-[120px] p-4 border-0 rounded-lg focus:ring-0 focus:outline-none transition-colors duration-200 resize-vertical placeholder-[#A4A4A4]"
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[#A4A4A4] mb-2">
                Attach relevant images*
              </label>
              <div className="relative">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                  id="image-upload"
                  disabled={isSubmitting}
                />
                <label
                  htmlFor="image-upload"
                  className="flex items-center justify-center w-full h-15 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors duration-200 cursor-pointer bg-gray-50 hover:bg-gray-100"
                >
                  <div className="flex items-center gap-2 text-gray-600">
                    <FiPlusCircle className="w-6 h-6 text-gray-400" />
                    <span>
                      {selectedImage ? selectedImage.name : "Upload Image"}
                    </span>
                  </div>
                </label>
              </div>
            </div>

            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="w-full cursor-pointer dark-accent text-white py-3 px-6 rounded-lg hover:bg-gray-800 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-transform duration-300 hover:scale-101 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Submitting...
                </div>
              ) : (
                "Submit"
              )}
            </button>
          </div>

          <div className="mt-4 px-4 flex items-start space-x-3  text-[#5D7285]">
            <img src={infoGif} alt="Info" className="w-8 h-8" />

            <p>
              Please note that queries will take some time to be answered,
              although we can assure you we will get to it as soon as possible.
            </p>
          </div>
        </div>

        {/* My Queries Section */}
        <div className="animate-slideUp" style={{ animationDelay: "0.1s" }}>
          <MyQueries />
        </div>

        {/* FAQ Section */}
        <div className="animate-slideUp" style={{ animationDelay: "0.2s" }}>
          <FAQ />
        </div>

        {/* Modal */}
        <Modal
          isOpen={modal.isOpen}
          onClose={closeModal}
          type={modal.type}
          title={modal.title}
          message={modal.message}
          icons={modal.icons}
        />
      </div>
    </div>
  );
};

export default Support;