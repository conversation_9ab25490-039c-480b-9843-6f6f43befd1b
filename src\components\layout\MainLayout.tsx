// import { Outlet, useRouter } from "@tanstack/react-router";
// import Navbar from "../navbar/Navbar";
// import "../../styles/tailwind.css";

// const HIDDEN_NAVBAR_ROUTES = ["/auth/login", "/auth/register"]; // Add other auth routes if needed

// export default function MainLayout() {
//   const router = useRouter();
//   const currentPath = router.state.location.pathname;

//   // Hide navbar on specific routes
//   const showNavbar = !HIDDEN_NAVBAR_ROUTES.includes(currentPath);

//   return (
//     <div>
//       <div className="transition-all duration-300">
//         {showNavbar && <Navbar />}
//       </div>
//       <main>
//         <Outlet />
//       </main>
//     </div>
//   );
// }

import { Outlet, useLocation } from "@tanstack/react-router";
import { useMemo } from "react";
import Navbar from "../navbar/Navbar";
import "../../styles/tailwind.css";

const HIDDEN_NAVBAR_ROUTES = ["/auth/login", "/auth/register"]; // Add other auth routes if needed

export default function MainLayout() {
  const location = useLocation();

  // More robust way to check current path
  const showNavbar = useMemo(() => {
    const currentPath = location.pathname;

    // Check exact matches
    if (HIDDEN_NAVBAR_ROUTES.includes(currentPath)) {
      return false;
    }

    // Check if any route starts with auth (for nested auth routes)
    if (currentPath.startsWith("/auth/")) {
      return false;
    }

    return true;
  }, [location.pathname]);

  return (
    <div>
      <div className="transition-all duration-300">
        {showNavbar && <Navbar />}
      </div>
      <main>
        <Outlet />
      </main>
    </div>
  );
}
