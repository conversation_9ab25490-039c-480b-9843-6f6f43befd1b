import React, { useState } from "react";
import { Plus<PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import infoGif from "../icons/info.gif";

interface QueryFormProps {
  onSubmit: (query: string, image: File | null) => Promise<void>;
}

const QueryForm: React.FC<QueryFormProps> = ({ onSubmit }) => {
  const [query, setQuery] = useState<string>("");
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [errors, setErrors] = useState<{
    query?: string;
    image?: string;
  }>({});

  const validateFile = (file: File): string | null => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      return "Please upload an image file (JPG, PNG, GIF, etc.)";
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      return "Please upload an image smaller than 5MB";
    }

    return null;
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setErrors((prev) => ({ ...prev, image: undefined }));

    if (file) {
      const error = validateFile(file);
      if (error) {
        setErrors((prev) => ({ ...prev, image: error }));
        event.target.value = ""; // Clear the input
        return;
      }
      setSelectedImage(file);
    }
  };

  const handleSubmit = async () => {
    // Reset errors
    setErrors({});

    // Validate query
    if (!query.trim()) {
      setErrors((prev) => ({
        ...prev,
        query: "Please enter your query before submitting",
      }));
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(query, selectedImage);
      // Reset form on successful submission
      setQuery("");
      setSelectedImage(null);
      // Clear file input
      const fileInput = document.getElementById(
        "image-upload"
      ) as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    } catch (error) {
      console.error("Submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeSelectedImage = () => {
    setSelectedImage(null);
    setErrors((prev) => ({ ...prev, image: undefined }));
    const fileInput = document.getElementById(
      "image-upload"
    ) as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-[#5D7285] mb-2">
        Raise a Query
      </h2>
      <p className="text-[#A4A4A4] mb-6">Ask your query</p>

      <div className="space-y-6">
        {/* Query Textarea */}
        <div>
          <div className="bg-white rounded-lg shadow-lg">
            <textarea
              value={query}
              onChange={(e) => {
                setQuery(e.target.value);
                setErrors((prev) => ({ ...prev, query: undefined }));
              }}
              placeholder="Enter Details"
              className="w-full min-h-[120px] p-4 border-0 rounded-lg focus:ring-0 focus:outline-none transition-colors duration-200 resize-vertical placeholder-[#A4A4A4]"
              disabled={isSubmitting}
            />
          </div>
          {errors.query && (
            <p className="text-red-500 text-sm mt-2">{errors.query}</p>
          )}
        </div>

        {/* Image Upload */}
        <div>
          <label className="block text-sm font-medium text-[#A4A4A4]mb-2">
            Attach relevant images*
          </label>
          <div className="relative">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
              id="image-upload"
              disabled={isSubmitting}
            />
            <label
              htmlFor="image-upload"
              className="flex items-center justify-center w-full h-15 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors duration-200 cursor-pointer bg-gray-50 hover:bg-gray-100"
            >
              <div className="flex items-center gap-2 text-gray-600">
                <PlusCircle className="w-6 h-6 text-gray-400" />
                <span>
                  {selectedImage ? selectedImage.name : "Upload Image"}
                </span>
              </div>
            </label>
          </div>

          {/* Selected Image Preview */}
          {selectedImage && (
            <div className="mt-3 flex items-center justify-between bg-blue-50 p-3 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <img
                    src={URL.createObjectURL(selectedImage)}
                    alt="Preview"
                    className="w-8 h-8 object-cover rounded"
                  />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {selectedImage.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(selectedImage.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <button
                onClick={removeSelectedImage}
                className="text-red-500 hover:text-red-700 text-sm font-medium"
                disabled={isSubmitting}
              >
                Remove
              </button>
            </div>
          )}

          {errors.image && (
            <p className="text-red-500 text-sm mt-2">{errors.image}</p>
          )}
        </div>

        {/* Submit Button */}
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="w-full cursor-pointer dark-accent text-white py-3 px-6 rounded-lg hover:bg-gray-800 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-transform duration-300 hover:scale-101 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center ">
              <Loader2 className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2" />
              Submitting...
            </div>
          ) : (
            "Submit"
          )}
        </button>

        {/* Info Note */}
        <div className="mt-4 px-4 flex items-start space-x-3  text-[#5D7285]">
          <img
            src={infoGif}
            alt="info icon"
            className="w-8 h-8 bg-transparent"
          />
          <p className="text-sm">
            Please note that queries will take some time to be answered,
            although we can assure you we will get to it as soon as possible.
          </p>
        </div>
      </div>
    </div>
  );
};

export default QueryForm;
