import React, { useState, useEffect, useRef } from "react";
import { TrendingUp } from "lucide-react";
import { FaCalendarDays } from "react-icons/fa6";

interface SpendData {
  date: string;
  clothing: number;
  movies: number;
  transport: number;
  entertainment: number;
  totalExpenditure: number;
}

interface WeeklySpendModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SpendingModal: React.FC<WeeklySpendModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [animateIn, setAnimateIn] = useState(false);

  // Date picker states
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedStartDate, setSelectedStartDate] = useState<string | null>(
    null
  );
  const [selectedEndDate, setSelectedEndDate] = useState<string | null>(null);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const datepickerRef = useRef<HTMLDivElement>(null);

  const spendData: SpendData[] = [
    {
      date: "May 1",
      clothing: 40,
      movies: 15,
      transport: 20,
      entertainment: 10,
      totalExpenditure: 85,
    },
    {
      date: "May 2",
      clothing: 0,
      movies: 0,
      transport: 15,
      entertainment: 25,
      totalExpenditure: 40,
    },
    {
      date: "May 3",
      clothing: 60,
      movies: 20,
      transport: 10,
      entertainment: 5,
      totalExpenditure: 95,
    },
    {
      date: "May 4",
      clothing: 30,
      movies: 10,
      transport: 12,
      entertainment: 20,
      totalExpenditure: 72,
    },
    {
      date: "May 5",
      clothing: 25,
      movies: 25,
      transport: 15,
      entertainment: 30,
      totalExpenditure: 95,
    },
    {
      date: "May 6",
      clothing: 10,
      movies: 0,
      transport: 20,
      entertainment: 5,
      totalExpenditure: 35,
    },
    {
      date: "May 7",
      clothing: 70,
      movies: 35,
      transport: 25,
      entertainment: 40,
      totalExpenditure: 170,
    },
    {
      date: "May 8",
      clothing: 20,
      movies: 10,
      transport: 15,
      entertainment: 10,
      totalExpenditure: 55,
    },
    {
      date: "May 9",
      clothing: 35,
      movies: 20,
      transport: 25,
      entertainment: 15,
      totalExpenditure: 95,
    },
    {
      date: "May 10",
      clothing: 0,
      movies: 0,
      transport: 10,
      entertainment: 0,
      totalExpenditure: 10,
    },
    {
      date: "May 11",
      clothing: 60,
      movies: 30,
      transport: 20,
      entertainment: 10,
      totalExpenditure: 120,
    },
    {
      date: "May 12",
      clothing: 15,
      movies: 10,
      transport: 12,
      entertainment: 8,
      totalExpenditure: 45,
    },
    {
      date: "May 13",
      clothing: 40,
      movies: 25,
      transport: 18,
      entertainment: 20,
      totalExpenditure: 103,
    },
    {
      date: "May 14",
      clothing: 22,
      movies: 0,
      transport: 14,
      entertainment: 5,
      totalExpenditure: 41,
    },
    {
      date: "May 15",
      clothing: 55,
      movies: 30,
      transport: 20,
      entertainment: 25,
      totalExpenditure: 130,
    },
    {
      date: "May 16",
      clothing: 5,
      movies: 15,
      transport: 10,
      entertainment: 5,
      totalExpenditure: 35,
    },
    {
      date: "May 17",
      clothing: 80,
      movies: 50,
      transport: 30,
      entertainment: 35,
      totalExpenditure: 195,
    },
  ];

  useEffect(() => {
    if (isOpen) {
      setAnimateIn(true);
    } else {
      setAnimateIn(false);
    }
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const formatCurrency = (amount: number) => {
    return amount === 0 ? "-" : `$${amount.toFixed(2)}`;
  };

  // Date picker functions
  const renderCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    const firstDayOfMonth = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const daysArray = [];

    for (let i = 0; i < firstDayOfMonth; i++) {
      daysArray.push(<div key={`empty-${i}`}></div>);
    }

    for (let i = 1; i <= daysInMonth; i++) {
      const day = new Date(year, month, i);
      const dayString = day.toLocaleDateString("en-US");
      let className =
        "flex h-[36px] w-[36px] items-center justify-center rounded-full hover:bg-gray-100 cursor-pointer text-sm";

      if (selectedStartDate && dayString === selectedStartDate) {
        className += " bg-blue-500 text-white rounded-r-none";
      }
      if (selectedEndDate && dayString === selectedEndDate) {
        className += " bg-blue-500 text-white rounded-l-none";
      }
      if (
        selectedStartDate &&
        selectedEndDate &&
        new Date(day) > new Date(selectedStartDate) &&
        new Date(day) < new Date(selectedEndDate)
      ) {
        className += " bg-blue-100 rounded-none";
      }

      daysArray.push(
        <div
          key={i}
          className={className}
          data-date={dayString}
          onClick={() => handleDayClick(dayString)}
        >
          {i}
        </div>
      );
    }

    return daysArray;
  };

  const handleDayClick = (selectedDay: string) => {
    if (!selectedStartDate || (selectedStartDate && selectedEndDate)) {
      setSelectedStartDate(selectedDay);
      setSelectedEndDate(null);
    } else {
      if (new Date(selectedDay) < new Date(selectedStartDate)) {
        setSelectedEndDate(selectedStartDate);
        setSelectedStartDate(selectedDay);
      } else {
        setSelectedEndDate(selectedDay);
      }
    }
  };

  const updateDateInput = () => {
    if (selectedStartDate && selectedEndDate) {
      return `${selectedStartDate} - ${selectedEndDate}`;
    } else if (selectedStartDate) {
      return selectedStartDate;
    } else {
      return "";
    }
  };

  const toggleDatepicker = () => {
    setIsDatePickerOpen(!isDatePickerOpen);
  };

  if (!isOpen) return null;

  return (
    <div
      className={`fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center z-50 p-4 transition-all duration-300 ${
        animateIn ? "opacity-100" : "opacity-0"
      }`}
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden transform transition-all duration-300 ${
          animateIn ? "scale-100 translate-y-0" : "scale-95 translate-y-8"
        }`}
      >
        {/* Header */}
        <div className="bg-white p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div>
                <h2 className="text-2xl font-bold text-[darkslateblue]">
                  Weekly Spend Breakdown
                </h2>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 cursor-pointer text-[tomato] font-bold hover:bg-gray-100 rounded-full transition-colors duration-200"
            >
              Close
            </button>
          </div>

          {/* Total Amount and Date Picker Section */}
          <div className="mt-6 grid grid-cols-1 lg:grid-cols-1 gap-6">
            {/* Total Amount */}
            <div className="bg-[azure] rounded-xl p-4 shadow-sm flex items-center justify-between">
              <div className="flex items-center justify-between gap-4">
                <div>
                  <p className="text-sm text-gray-600">Total Spend</p>
                  <p className="text-3xl font-bold text-[slategray]">$560.00</p>
                </div>
                <div className="flex items-center space-x-2 ">
                  <TrendingUp className="w-5 h-5 text-[forestgreen]" />
                  <span className="text-sm text-[steelblue]"> This week</span>
                  <span className="text-sm font-medium text-[forestgreen]">
                    {"    "}
                    +12%
                  </span>
                </div>
              </div>
              {/* Date Picker */}
              <div className="rounded-full bg-white shadow-md  w-[200px]">
                <div className="relative" ref={datepickerRef}>
                  <div
                    onClick={toggleDatepicker}
                    className="w-full flex items-center justify-between px-4 py-2 rounded-full bg-white text-gray-500 text-sm cursor-pointer hover:border-teal-500 transition"
                  >
                    <span>{updateDateInput() || "Date"}</span>
                    <FaCalendarDays className="text-[teal] text-2xl" />
                  </div>

                  {/* Datepicker popup */}
                  {isDatePickerOpen && (
                    <div className="absolute top-full right-0 mt-2 z-20 w-[300px] sm:w-[360px] bg-white rounded-lg border border-gray-200 shadow-lg p-4">
                      <div className="flex items-center justify-between pb-3 border-b border-gray-100">
                        <p className="text-sm font-medium text-gray-900">
                          {currentDate.toLocaleString("default", {
                            month: "long",
                          })}{" "}
                          {currentDate.getFullYear()}
                        </p>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() =>
                              setCurrentDate(
                                new Date(
                                  currentDate.setMonth(
                                    currentDate.getMonth() - 1
                                  )
                                )
                              )
                            }
                            className="flex h-6 w-6 items-center justify-center rounded border border-gray-300 bg-gray-50 text-gray-600 hover:bg-blue-500 hover:text-white transition-colors duration-200"
                          >
                            <svg
                              width="12"
                              height="12"
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="fill-current"
                            >
                              <path d="M10.825 14.325C10.675 14.325 10.525 14.275 10.425 14.15L4.77501 8.40002C4.55001 8.17502 4.55001 7.82502 4.77501 7.60002L10.425 1.85002C10.65 1.62502 11 1.62502 11.225 1.85002C11.45 2.07502 11.45 2.42502 11.225 2.65002L5.97501 8.00003L11.25 13.35C11.475 13.575 11.475 13.925 11.25 14.15C11.1 14.25 10.975 14.325 10.825 14.325Z" />
                            </svg>
                          </button>
                          <button
                            onClick={() =>
                              setCurrentDate(
                                new Date(
                                  currentDate.setMonth(
                                    currentDate.getMonth() + 1
                                  )
                                )
                              )
                            }
                            className="flex h-6 w-6 items-center justify-center rounded border border-gray-300 bg-gray-50 text-gray-600 hover:bg-blue-500 hover:text-white transition-colors duration-200"
                          >
                            <svg
                              width="12"
                              height="12"
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="fill-current"
                            >
                              <path d="M5.17501 14.325C5.02501 14.325 4.90001 14.275 4.77501 14.175C4.55001 13.95 4.55001 13.6 4.77501 13.375L10.025 8.00003L4.77501 2.65002C4.55001 2.42502 4.55001 2.07502 4.77501 1.85002C5.00001 1.62502 5.35001 1.62502 5.57501 1.85002L11.225 7.60002C11.45 7.82502 11.45 8.17502 11.225 8.40002L5.57501 14.15C5.47501 14.25 5.32501 14.325 5.17501 14.325Z" />
                            </svg>
                          </button>
                        </div>
                      </div>

                      {/* Day names */}
                      <div className="grid grid-cols-7 gap-1 py-2 text-xs font-medium text-gray-500">
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                          (day) => (
                            <div
                              key={day}
                              className="flex h-8 w-8 items-center justify-center"
                            >
                              {day}
                            </div>
                          )
                        )}
                      </div>

                      {/* Calendar body */}
                      <div className="grid grid-cols-7 gap-1 text-sm">
                        {renderCalendar()}
                      </div>

                      {/* Footer with selected range */}
                      <div className="flex items-center justify-between pt-3 border-t border-gray-100 mt-3">
                        <button className="px-3 py-1 text-xs rounded border border-gray-300 bg-gray-50 text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                          {selectedStartDate || "Start Date"}
                        </button>
                        <span className="text-gray-400">to</span>
                        <button className="px-3 py-1 text-xs rounded border border-gray-300 bg-gray-50 text-gray-600 hover:bg-gray-100 transition-colors duration-200">
                          {selectedEndDate || "End Date"}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content - Table View */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-280px)] scrollbar-hide">
          <div className="overflow-x-auto rounded-2xl shadow-sm">
            <table className="w-full min-w-[800px] text-sm text-left text-gray-700 bg-white border border-gray-200 rounded-2xl overflow-hidden">
              <thead className="bg-gray-100 text-gray-600 uppercase text-xs font-semibold sticky top-0  z-10 shadow-sm">
                <tr>
                  <th className="py-4 px-6">Date</th>
                  <th className="py-4 px-6 text-center">Clothing</th>
                  <th className="py-4 px-6 text-center">Movies</th>
                  <th className="py-4 px-6 text-center">Transport</th>
                  <th className="py-4 px-6 text-center">Entertainment</th>
                  <th className="py-4 px-6 text-center">Total</th>
                </tr>
              </thead>
              <tbody>
                {spendData.map((day, index) => (
                  <tr
                    key={day.date}
                    className={`transition-colors duration-200 hover:bg-teal-50 ${
                      animateIn ? "animate-fadeInUp" : ""
                    }`}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <td className="py-4 px-6 font-medium text-gray-900">
                      {day.date}
                    </td>
                    <td className="py-4 px-6 text-center text-gray-600">
                      {formatCurrency(day.clothing)}
                    </td>
                    <td className="py-4 px-6 text-center text-gray-600">
                      {formatCurrency(day.movies)}
                    </td>
                    <td className="py-4 px-6 text-center text-gray-600">
                      {formatCurrency(day.transport)}
                    </td>
                    <td className="py-4 px-6 text-center text-gray-600">
                      {formatCurrency(day.entertainment)}
                    </td>
                    <td className="py-4 px-6 text-center font-semibold text-teal-700">
                      {formatCurrency(day.totalExpenditure)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpendingModal;
