import { RiBar<PERSON>hartFill } from "react-icons/ri";
import { FaRegClock } from "react-icons/fa";
import { FaRupeeSign, FaReceipt } from "react-icons/fa";
import VerticalDivider from "../../components/VerticalDivider";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  ResponsiveContainer,
  Tooltip,
} from "recharts";
import { FaCaretUp } from "react-icons/fa";
import { FaRegCalendar } from "react-icons/fa";
import { FaCheck } from "react-icons/fa6";
import ExpenseTable from "../../components/tables/ExpenseTable";
import DisputeTable from "../../components/tables/DisputeTable";

const data = [
  { month: "SEP", value: 120, budget: 105 },
  { month: "OCT", value: 145, budget: 105 },
  { month: "NOV", value: 150, budget: 115 },
  { month: "DEC", value: 120, budget: 105 },
  { month: "JAN", value: 90, budget: 120 },
  { month: "FEB", value: 90, budget: 125 },
]

const Home = () => {
  return (
    <>
      <div className="min-h-screen max-w-7xl mx-auto p-3 sm:p-4 lg:p-5">
        {/* Main Grid Container */}
        <div className="grid grid-cols-1 lg:grid-cols-2 auto-rows-auto gap-3 sm:gap-4">
          {/* Top Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4">
            <div className="bg-white rounded-xl shadow p-4 sm:p-5 flex items-center gap-3 sm:gap-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-orange-100">
                <RiBarChartFill className="text-orange-700" size={16} />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-[slategray] font-bold">
                  Pending
                </p>
                <p className="text-lg sm:text-xl font-bold text-gray-700">4</p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow p-4 sm:p-5 flex items-center gap-3 sm:gap-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-green-100">
                <RiBarChartFill className="text-green-700" size={16} />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-[slategray] font-bold">
                  Approved
                </p>
                <p className="text-lg sm:text-xl font-bold text-gray-700">15</p>
              </div>
            </div>
            <div className="bg-white rounded-xl shadow p-4 sm:p-5 flex items-center gap-3 sm:gap-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 flex items-center justify-center rounded-full bg-red-100">
                <RiBarChartFill className="text-red-700" size={16} />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-[slategray] font-bold">
                  Rejected
                </p>
                <p className="text-lg sm:text-xl font-bold text-gray-700">6</p>
              </div>
            </div>
          </div>

          {/* Common Category & Approval Time Card */}
          <div className="bg-white rounded-xl shadow p-4 sm:p-6 flex flex-col md:flex-row md:space-x-6 lg:space-x-10 space-y-4 md:space-y-0">
            {/* Section 1: Common Category Expenditure */}
            <div className="flex-1 flex flex-col gap-2">
              <div className="flex gap-2 items-center">
                <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-md bg-sky-900">
                  <img
                    src="/homePageCommanCategoryIcon.svg"
                    alt="brand-logo"
                    className="w-3.5 h-3.5 sm:w-4.5 sm:h-4.5 object-contain"
                  />
                </div>
                <p className="text-xs sm:text-sm font-medium text-[slategray]">
                  Common Category Expenditure
                </p>
              </div>
              <p className="text-xs sm:text-sm font-bold text-gray-900">
                Travel & Transportation
              </p>
            </div>

            {/* Divider (Only on medium and up) */}
            <div className="hidden md:block">
              <VerticalDivider />
            </div>

            {/* Section 2: Average Approval Time */}
            <div className="flex-1 flex flex-col gap-2">
              <div className="flex gap-2 items-center">
                <div className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-md bg-sky-900">
                  <FaRegClock className="text-white text-base sm:text-lg" />
                </div>
                <p className="text-xs sm:text-sm font-medium text-[slategray]">
                  Average Approval Time
                </p>
              </div>
              <p className="text-xs sm:text-sm font-bold text-gray-900">
                Your claims are usually approved in{" "}
                <span className="text-cyan-600 font-semibold">1 to 2</span> days
              </p>
            </div>
          </div>


          {/* Usage Limit Card */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-[steelblue] h-2 rounded-full"
                style={{ width: "20%" }}
              ></div>
            </div>
            <div className="flex justify-between items-center mt-4">
              <span className="text-[slategray] font-bold text-sm sm:text-base">
                Usage Limit
              </span>
              <span className="text-[slategray] font-semibold text-sm sm:text-base">
                $350.60 / $50000
              </span>
            </div>
          </div>

          {/* Historical Trend Chart */}
          <div className="bg-white rounded-xl border border-gray-200 p-4 sm:p-6 shadow-sm lg:row-span-2">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-12 gap-4">
              <div className="text-sm sm:text-md font-bold text-[darkslateblue]">
                Historical Trend
              </div>
              <div className="flex items-center gap-2 sm:gap-3">
                <button
                  className="px-3 sm:px-5 py-2 sm:py-2.5 rounded-lg text-xs text-[slategray] flex items-center gap-2 sm:gap-3 bg-blue-50"
                  onClick={() => { }}
                >
                  <FaRegCalendar className="w-3 h-3 sm:w-4 sm:h-4 text-[slategray]" />
                  <span className="hidden sm:inline">This Year</span>
                  <span className="sm:hidden">Year</span>
                </button>
                <button
                  className="p-2 rounded-lg text-gray-500 bg-blue-50"
                  title="View chart"
                  onClick={() => { }}
                >
                  <RiBarChartFill className="w-3 h-3 sm:w-4 sm:h-4 text-teal-600" />
                </button>
              </div>
            </div>

            {/* Metrics */}
            <div className="mb-6 sm:mb-8">
              <div className="mb-2">
                <span className="text-2xl sm:text-3xl font-bold text-[slategray]">
                  $37.5K
                </span>
              </div>
              <div className="flex items-baseline gap-2 sm:gap-3">
                <div className="text-xs sm:text-sm text-[slategray] mb-4 font-semibold">
                  Total Spent
                </div>
                <div className="text-xs sm:text-sm text-green-400 font-bold flex items-center gap-1">
                  <FaCaretUp />
                  <span>+2.45%</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-400 p-1 rounded-full flex items-center justify-center">
                  <FaCheck className="text-white w-3 h-3" />
                </div>
                <span className="text-sm sm:text-md font-bold text-green-400">
                  On track
                </span>
              </div>
            </div>

            <div className="h-32 sm:h-48 relative">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={data}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <XAxis
                    dataKey="month"
                    axisLine={false}
                    tickLine={false}
                    tick={{
                      fontSize: 14,
                      fill: "#9CA3AF",
                      fontWeight: "bold",
                    }}
                    dy={10}
                  />
                  <YAxis hide />

                  <Line
                    type="monotone"
                    dataKey="budget"
                    stroke="#08ccb5"
                    strokeWidth={3}
                    dot={false}
                    strokeDasharray="none"
                    activeDot={{
                      r: 4,
                      fill: "white",
                      stroke: "#08ccb5",
                      strokeWidth: 2,
                    }}
                  />

                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#009191"
                    strokeWidth={3}
                    dot={false}
                    activeDot={{
                      r: 4,
                      fill: "white",
                      stroke: "#009191",
                      strokeWidth: 2,
                    }}
                  />

                  <Tooltip
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-teal-500 text-white p-2 sm:p-3 rounded-lg text-xs font-medium shadow-lg">
                            <div className="flex flex-col gap-1">
                              <div>Actual: ${payload[0].value}.00</div>
                              <div>Budget: ${payload[1].value}.00</div>
                            </div>
                          </div>
                        );
                      }
                      return null;
                    }}
                    cursor={false}
                    allowEscapeViewBox={{ x: false, y: true }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Latest Reimbursement Request */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 gap-4">
              <div>
                <div className="text-sm sm:text-md font-bold text-gray-800 mb-3 sm:mb-5">
                  Latest Reimbursement Request
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                  <div className="text-lg sm:text-2xl font-bold text-[slategray]">
                    Travel to client location
                  </div>
                  <div className="text-[slategray] text-sm sm:text-2xl">
                    12/12/2022
                  </div>
                </div>
              </div>
              <div className="brand-gradient text-white px-6 sm:px-12 py-2 sm:py-3 rounded-full text-xs font-medium self-start">
                Submitted
              </div>
            </div>

            {/* Amount and Type */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-8 mb-6 sm:mb-12 mt-4 sm:mt-6">
              <div className="flex items-center gap-2">
                <div className="latest-reimbursement-card-gradient p-2 rounded-lg">
                  <FaRupeeSign className="text-white text-sm sm:text-lg" />
                </div>
                <span className="text-base sm:text-lg font-bold text-[darkslateblue]">
                  8000.00
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="latest-reimbursement-card-gradient p-2 rounded-lg">
                  <FaReceipt className="text-white text-sm sm:text-lg" />
                </div>
                <span className="text-[darkslateblue] font-bold text-sm sm:text-base">
                  Multiple Expenses
                </span>
              </div>
            </div>

            {/* Expense Categories */}
            <div className="mb-4 sm:mb-6 bg-gray-50 p-3 rounded-lg">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                <span className="text-[slategray] font-medium text-sm sm:text-base">
                  Expense Categories
                </span>
                <div className="hidden sm:block">
                  <VerticalDivider />
                </div>
                <div className="flex gap-2">
                  <img
                    src="palneIcon.svg"
                    alt="travel"
                    className="text-white text-sm w-4 h-4 sm:w-5 sm:h-5"
                  />
                  <img
                    src="burgerIcon.svg"
                    alt="food"
                    className="text-white text-sm w-4 h-4 sm:w-5 sm:h-5"
                  />
                </div>
              </div>
            </div>

            {/* Approval Workflow */}
            <div className="bg-teal-800 rounded-lg p-3 sm:p-4">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
                <div className="flex items-center gap-2 text-white">
                  <img
                    src="successIconGreen.svg"
                    alt="manager"
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                  <span className="font-medium text-sm sm:text-base">
                    Manager
                  </span>
                </div>

                <div className="hidden sm:block">
                  <VerticalDivider />
                </div>

                <div className="flex items-center gap-2 text-white">
                  <img
                    src="successIconWhite.svg"
                    alt="finance dept"
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                  <span className="font-medium text-sm sm:text-base">
                    Finance Dept
                  </span>
                </div>

                <div className="hidden sm:block">
                  <VerticalDivider />
                </div>

                <div className="flex items-center gap-2 text-white">
                  <img
                    src="successIconWhite.svg"
                    alt="payment release"
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                  <span className="font-medium text-sm sm:text-base">
                    Payment Release
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Expense Table */}
        <div className="mt-4 sm:mt-5">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <ExpenseTable />
            </div>
          </div>
        </div>

        {/* Dispute Table */}
        <div className="mt-4 sm:mt-5">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <DisputeTable />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Home;
