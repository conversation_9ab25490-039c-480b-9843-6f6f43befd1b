// {
//   "compilerOptions": {
//     "noImplicitAny": true,
//     // "skipLibCheck": true,
//     "typeRoots": ["./src/types", "./node_modules/@types"],
//     "types": ["node", "react", "react-dom"]
//   },
//   "files": [],
//   "references": [
//     { "path": "./tsconfig.app.json" },
//     { "path": "./tsconfig.node.json" }
//   ],
//   "include": ["src/**/*"]
// }
{
  "compilerOptions": {
    "target": "ES2015",
    "noImplicitAny": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "jsx": "react-jsx",
    "typeRoots": ["./src/types", "./node_modules/@types"],
    "types": ["node", "react", "react-dom"]
  },
  "files": [],
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ],
  "include": ["src/**/*"]
}
