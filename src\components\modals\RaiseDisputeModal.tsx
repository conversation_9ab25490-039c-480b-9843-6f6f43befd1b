import React, { useState } from "react";
import disputeRaisedSuccessfully from "../icons/disputeRaisedSuccessfully.gif";
import TrackDisputeModal from "./TrackDisputeModal";
import type { DisputeData } from "../../types/expenseTypes";
interface DisputeModalProps {
  isRaiseDisputeModalOpen: boolean;
  handleCloseRaiseDisputeModal: () => void;
  handleCloseTackerDispute: () => void;
}

const RaiseDisputeModal: React.FC<DisputeModalProps> = ({
  isRaiseDisputeModalOpen,
  handleCloseRaiseDisputeModal,
  handleCloseTackerDispute,
}) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [disputeReason, setDisputeReason] = useState("");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isTrackDisputeModalOpen, setIsTrackDisputeModalOpen] =
    useState<boolean>(false);

  const selectedExpense: DisputeData = {
    id: "1",
    title: "Travel to client location",
    description:
      "Exploring the world through travel and meeting clients in person for project discussions and collaboration.",
    category: "Multiple Expenses",
    date: "2024-07-18",
    submissionDate: "2024-07-20",
    amount: "$8000",
    currency: "USD",
    status: "Rejected",
    disputeReason: "Finance rejected due to unclear purpose of travel.",
    approvals: [
      { stage: "Manager", status: "Approved" },
      { stage: "Finance", status: "Rejected" },
      { stage: "Payment", status: "Pending" },
    ],
    attachedEvidence: [
      {
        name: "Receipt1.jpeg",
        url: "https://s3-ap-southeast-1.amazonaws.com/lc-public-content/userattachments/33146/Le_Chef_Receipt____20180521103247___.jpeg",
      },
      {
        name: "TaxiBill.jpg",
        url: "https://tse2.mm.bing.net/th/id/OIP.E0aviGYW0mCEd40R_ANCtgHaM4",
      },
      {
        name: "MealInvoice.jpg",
        url: "https://tse4.mm.bing.net/th/id/OIP.KYcBWysJgSeA8B0GZXEKawHaKV",
      },
    ],
  };

  const handleSubmit = () => {
    if (disputeReason.trim()) {
      setIsSubmitted(true);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleBackToHome = () => {
    setIsSubmitted(false);
    setDisputeReason("");
    setUploadedFile(null);
    handleCloseRaiseDisputeModal();
    handleCloseTackerDispute();
  };

  const handleCancel = () => {
    setIsSubmitted(false);
    setDisputeReason("");
    setUploadedFile(null);
    handleCloseRaiseDisputeModal();
  };
  const handleOpenTrackDispute = () => {
    setIsTrackDisputeModalOpen(true);
  };

  const handleCloseTrackDispute = () => {
    setIsTrackDisputeModalOpen(false);
    handleCloseRaiseDisputeModal();
    handleCloseTackerDispute();
  };

  if (!isRaiseDisputeModalOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50 animate-fadeIn duration-300">
        <div className="bg-white rounded-2xl w-full max-w-4xl mx-auto shadow-2xl transform animate-slideIn transition-all duration-300">
          {!isSubmitted ? (
            // Dispute Form
            <div className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-[darkslateblue]">
                  Raise Dispute
                </h2>
              </div>

              <div className="flex flex-col md:flex-row gap-6 items-stretch">
                {/* Attach Evidence Section */}
                <div className="flex-1 flex flex-col">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Attach evidence
                  </h3>
                  <div className="flex-1 border-2  border-dashed border-gray-300 rounded-lg p-8 text-center bg-gray-50 hover:bg-gray-100 transition-colors flex flex-col justify-center">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-1">
                          Upload Receipt
                        </p>
                        <p className="text-xs text-gray-500">
                          Drag and drop or click to upload
                        </p>
                      </div>
                      <input
                        type="file"
                        onChange={handleFileUpload}
                        className="hidden"
                        id="file-upload"
                        accept=".pdf,.jpg,.jpeg,.png"
                      />
                      <label
                        htmlFor="file-upload"
                        className="inline-block darkAccent px-8 py-2 rounded-lg text-sm font-medium hover:bg-slate-700 transition-colors cursor-pointer hover:scale-105"
                      >
                        Upload
                      </label>
                      {uploadedFile && (
                        <p className="text-xs text-green-600 mt-2">
                          {uploadedFile.name} uploaded successfully
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Dispute Reason Section */}
                <div className="flex-1 flex flex-col">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    Dispute Reason
                  </h3>
                  <div className="flex-1 flex flex-col">
                    <textarea
                      value={disputeReason}
                      onChange={(e) => setDisputeReason(e.target.value)}
                      placeholder="Enter Description"
                      className="flex-1 p-3 border border-gray-300 rounded-lg resize-none focus:outline-none active:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent placeholder-[slategray]"
                    />
                  </div>
                </div>
              </div>

              {/* Buttons below both sections */}
              <div className="flex flex-col md:flex-row justify-end gap-4 pt-4">
                <button
                  onClick={handleCancel}
                  className="cursor-pointer  bg-gray-400 text-white py-3 px-12  rounded-lg font-medium hover:bg-gray-500 transition-colors hover:scale-105"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  disabled={!disputeReason.trim()}
                  className="cursor-pointer darkAccent py-3 px-12 rounded-lg font-medium hover:bg-slate-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105"
                >
                  Submit
                </button>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center space-y-6">
              <div className="mx-auto w-full flex justify-center items-center">
                <img
                  src={disputeRaisedSuccessfully}
                  alt="successfully raised gif"
                  className="w-[334px] h-[334px]"
                />
              </div>

              <div className="space-y-2">
                <h2 className="text-xl font-semibold text-gray-800">
                  Submitted Successfully!
                </h2>
                <p className="text-sm text-gray-500 leading-relaxed">
                  You have successfully raised a dispute to your employer
                </p>
              </div>

              <div className="flex justify-between items-center gap-6">
                <button
                  onClick={handleOpenTrackDispute}
                  className=" cursor-pointer text-[slategray] w-md py-3 px-4 rounded-lg font-medium border-2 border-[steelblue] transition-colors hover:scale-105 transform transform-all duration-300"
                >
                  Track Dispute
                </button>
                <button
                  onClick={handleBackToHome}
                  className=" cursor-pointer w-md darkAccent py-3 px-4 rounded-lg font-medium hover:bg-slate-700 transition-colors hover:scale-105 transform transform-all duration-300  "
                >
                  Back to Home
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      {isTrackDisputeModalOpen && (
        <TrackDisputeModal
          isTrackDisputeModalOpen={isTrackDisputeModalOpen}
          handleCloseTrackDisputeModal={handleCloseTrackDispute}
          selectedExpense={selectedExpense}
        />
      )}
    </>
  );
};

export default RaiseDisputeModal;
