import React from "react";
import addExpenseSuccessful from "../icons/addExpenseSuccessful.gif";

type SuccessModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const SuccessModal: React.FC<SuccessModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-[rgba(0,0,0,0.66)] backdrop-blur-[6.5px] flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center">
        <div className="mx-auto w-full max-w-[200px]">
          <img
            src={addExpenseSuccessful}
            alt="successfully raised gif"
            className="w-full h-auto object-contain"
          />
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Submitted Successfully!
        </h2>
        <p className="text-gray-500 mb-6">
          You have successfully submitted your expense report to your employer
        </p>

        <button
          onClick={onClose}
          className="w-full bg-slate-600 text-white py-3 px-6 rounded-lg hover:bg-slate-700 transition-colors font-medium"
        >
          Back to Home
        </button>
      </div>
    </div>
  );
};

export default SuccessModal;
