import { useAuth } from "../../hooks/useAuth";
import FinanceApprovals from "../../pages/financeApprovals/financeApprovals";
import ManagerApprovals from "../../pages/managerApprovals/managerApprovals";

export default function ApprovalsContainer() {
  const { isManager, isFinance } = useAuth();

  if (isManager) return <ManagerApprovals />;
  if (isFinance) return <FinanceApprovals />;

  return <div>Access Denied</div>;
}
