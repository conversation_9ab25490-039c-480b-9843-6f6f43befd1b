import React, { useEffect, useState } from "react";

import {
  FaChevronRight,
  Fa<PERSON>ser,
  FaBell,
  FaShieldAlt,
  FaQuestionCircle,
  FaSignOutAlt,
  FaExclamationTriangle,
} from "react-icons/fa";
import { RiUserSettingsLine } from "react-icons/ri";
import { useAuth } from "../../hooks/useAuth";
import { useNavigate } from "@tanstack/react-router";

interface SettingsItem {
  id: string;
  title: string;
  icon?: React.ReactNode;
  hasSubpage?: boolean;
  type?: "toggle" | "link" | "action";
  enabled?: boolean;
  color?: "default" | "danger";
}

interface SettingsSection {
  title: string;
  items: SettingsItem[];
}

const ProfileSettings: React.FC = () => {
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);
  const { logout } = useAuth();
  const navigate = useNavigate();
  const settingSections: SettingsSection[] = [
    {
      title: "Profile Information",
      items: [
        {
          id: "account-info",
          title: "Account Information",
          icon: <FaUser className="w-5 h-5" aria-hidden="true" />,
          hasSubpage: true,
          type: "link",
        },
      ],
    },
    {
      title: "Notification Settings",
      items: [
        {
          id: "email-notifications",
          title: "Email Notifications",
          icon: <FaBell className="w-5 h-5" aria-hidden="true" />,
          type: "toggle",
          enabled: emailNotifications,
        },
        {
          id: "push-notifications",
          title: "Push Notifications",
          icon: <FaBell className="w-5 h-5" aria-hidden="true" />,
          type: "toggle",
          enabled: pushNotifications,
        },
      ],
    },
    {
      title: "App",
      items: [
        {
          id: "privacy-policy",
          title: "Privacy Policy",
          icon: <FaShieldAlt className="w-5 h-5" aria-hidden="true" />,
          hasSubpage: true,
          type: "link",
        },
        {
          id: "support",
          title: "Support",
          icon: <FaQuestionCircle className="w-5 h-5" aria-hidden="true" />,
          hasSubpage: true,
          type: "link",
        },
      ],
    },
  ];

  const logoutItem: SettingsItem = {
    id: "logout",
    title: "Logout",
    icon: <FaSignOutAlt className="w-5 h-5" aria-hidden="true" />,
    type: "action",
    color: "danger",
  };

  const handleToggle = (itemId: string) => {
    if (itemId === "email-notifications") {
      setEmailNotifications(!emailNotifications);
    }
    if (itemId === "push-notifications") {
      setPushNotifications(!pushNotifications);
    }
  };

  const handleNavigation = (itemId: string) => {
    // Use window.location for navigation to avoid strict typing issues
    const routeMap: Record<string, string> = {
      "account-info": "/profile/account-information",
      "privacy-policy": "/profile/privacy-policy",
      support: "/profile/support",
    };

    const route = routeMap[itemId];
    if (route) {
      window.location.href = route;
    }
  };

  const confirmLogout = async () => {
    try {
      // First perform the logout
      await logout();

      // Then navigate using useNavigate
      await navigate({ to: "/auth/login" });

      // Clean up modal state
      setShowLogoutModal(false);
      setDontShowAgain(false);
    } catch (error) {
      console.error("Logout failed:", error);
      // Handle error if needed
    }
  };

  const handleLogout = () => setShowLogoutModal(true);
  const cancelLogout = () => {
    setShowLogoutModal(false);
    setDontShowAgain(false);
  };

  const renderToggleSwitch = (enabled: boolean, onChange: () => void) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        checked={enabled}
        onChange={onChange}
        className="sr-only peer"
        aria-label={enabled ? "Disable setting" : "Enable setting"}
      />
      <div
        className={`relative w-11 h-6 rounded-full peer transition-colors duration-200 ${
          enabled ? "bg-green-500" : "bg-gray-300"
        }`}
      >
        <div
          className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform duration-200 ${
            enabled ? "translate-x-5" : "translate-x-0"
          }`}
        />
      </div>
    </label>
  );

  const renderSettingsItem = (item: SettingsItem) => {
    const baseClasses =
      "flex items-center justify-between p-4 rounded-lg hover:bg-gray-100 transition-colors duration-200 bg-[#F5F5F5] shadow-sm";
    const textColor =
      item.color === "danger" ? "text-red-500" : "text-gray-700";

    return (
      <div
        key={item.id}
        className={`${baseClasses} ${
          item.type === "link" || item.type === "action" ? "cursor-pointer" : ""
        }`}
        onClick={() => {
          if (item.type === "link") handleNavigation(item.id);
          if (item.type === "action" && item.id === "logout") handleLogout();
        }}
        role={
          item.type === "link" || item.type === "action" ? "button" : undefined
        }
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            if (item.type === "link") handleNavigation(item.id);
            if (item.type === "action" && item.id === "logout") handleLogout();
          }
        }}
      >
        <div className="flex items-center space-x-3">
          <div
            className={`${
              item.color === "danger" ? "text-red-500" : "text-gray-500"
            }`}
          >
            {item.icon}
          </div>
          <span className={`font-medium ${textColor}`}>{item.title}</span>
        </div>

        <div className="flex items-center">
          {item.type === "toggle" &&
            renderToggleSwitch(!!item.enabled, () => handleToggle(item.id))}
          {item.hasSubpage && (
            <FaChevronRight
              className="w-5 h-5 text-gray-400"
              aria-hidden="true"
            />
          )}
        </div>
      </div>
    );
  };

  const LogoutModal = () => (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center animate-fadeIn z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full mx-4 shadow-xl transform animate-slideIn">
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
              <FaExclamationTriangle
                className="w-6 h-6 text-red-500"
                aria-hidden="true"
              />
            </div>
            <h2 className="text-xl font-bold text-gray-900">Logout</h2>
          </div>
          <p className="text-gray-600 mb-6">
            Logging out will permanently remove your session from this device.
          </p>
          <div className="flex items-center mb-6">
            <input
              type="checkbox"
              id="dontShowAgain"
              checked={dontShowAgain}
              onChange={(e) => setDontShowAgain(e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <label
              htmlFor="dontShowAgain"
              className="ml-2 text-sm text-gray-600"
            >
              Don't show again
            </label>
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={cancelLogout}
              className="px-4 py-2 cursor-pointer text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium"
            >
              Cancel
            </button>
            <button
              onClick={confirmLogout}
              className="px-4 py-2 cursor-pointer bg-red-500 text-white rounded-lg hover:bg-red-600 font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 pt-5">
      <div
        className={`max-w-5xl mx-auto p-4 sm:p-6 bg-white mt-4 rounded-lg shadow-lg transform transition-all duration-700 ease-in-out ${
          isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"
        }`}
      >
        <div className="mb-6 transition-all duration-700 delay-100">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-500 flex items-center justify-start gap-2 mb-4">
            <RiUserSettingsLine aria-hidden="true" />
            <span>Profile Settings</span>
          </h1>
        </div>

        <div
          className="space-y-8 animate-slideUp"
          style={{ animationDelay: "0.2s" }}
        >
          {settingSections.map((section, index) => (
            <div key={index} className="space-y-4">
              <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wider">
                {section.title}
              </h2>
              <div className="space-y-2">
                {section.items.map(renderSettingsItem)}
              </div>
            </div>
          ))}
          <div className="border-gray-200">
            {renderSettingsItem(logoutItem)}
          </div>
        </div>
      </div>

      {showLogoutModal && <LogoutModal />}
    </div>
  );
};

export default ProfileSettings;
