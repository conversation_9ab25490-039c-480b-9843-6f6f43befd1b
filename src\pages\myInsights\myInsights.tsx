import React, { useState } from "react";
import { FaDollarSign, FaEllipsisH } from "react-icons/fa";
import budgetUtilization from "../../components/icons/budgetUtilization.svg";
import categoryExpenditure from "../../components/icons/categoryExpenditure.svg";
import policyCompliance from "../../components/icons/policyCompliance.svg";
import averageApprovalTime from "../../components/icons/averageApprovalTime.svg";
import unusualHighTransaction from "../../components/icons/unusualHighTransaction.svg";
import saveByBookingEarly from "../../components/icons/saveByBookingEarly.svg";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  //   Legend,
  ResponsiveContainer,
  type TooltipProps,
  type DotProps,
  BarChart,
  ReferenceLine,
  Bar,
  LabelList,
} from "recharts";
import SpendingModal from "../../components/modals/SpendingModal";

const MyInsights: React.FC = () => {
  const [isWeeklySpendModalOpen, setWeeklySpendModalOpen] =
    useState<boolean>(false);

  type ChartData = {
    name: string;
    expenses: number;
    budget: number;
  };
  const data: ChartData[] = [
    { name: "Mon", expenses: 8500, budget: 4000 },
    { name: "Tue", expenses: 12400, budget: 5000 },
    { name: "Wed", expenses: 17900, budget: 7500 },
    { name: "Thu", expenses: 6800, budget: 6800 }, // << Match: tooltip will show
    { name: "Fri", expenses: 26300, budget: 8800 },
    { name: "Sat", expenses: 32000, budget: 11000 },
    { name: "Sun", expenses: 28400, budget: 9600 },
  ];
  // Add this inside MyInsights component (above return)

  const statCards = [
    {
      title: "Budget Utilization",
      description: (
        <>
          You have used{" "}
          <span className="text-[seagreen] font-semibold">30%</span> of your
          current budget
        </>
      ),
      color: "seagreen",
      icon: budgetUtilization,
    },
    {
      title: "Category Expenditure",
      description: (
        <>
          Meals spending is{" "}
          <span className="text-[steelblue] font-semibold">16%</span> above
          average
        </>
      ),
      color: "steelblue",
      icon: categoryExpenditure,
    },
    {
      title: "Average Approval Time",
      description: (
        <>
          Your claims are usually approved in{" "}
          <span className="text-[darkcyan] font-semibold">1 to 2 days</span>
        </>
      ),
      color: "darkcyan",
      icon: averageApprovalTime,
    },
    {
      title: "Policy Compliance",
      description: (
        <>
          <span className="text-[goldenrod] font-semibold">2</span> Claims in
          dispute and currently under investigation
        </>
      ),
      color: "goldenrod",
      icon: policyCompliance,
    },
  ];

  type SpendingData = {
    day: number;
    amount: number;
    isToday?: boolean;
  };

  const barData: SpendingData[] = [
    { day: 10, amount: 20 },
    { day: 11, amount: 60, isToday: true },
    { day: 12, amount: 25 },
    { day: 13, amount: 50 },
    { day: 14, amount: 50 },
  ];

  interface CustomLabelProps {
    x?: number;
    y?: number;
    index?: number;
  }

  const CustomTodayLabel: React.FC<CustomLabelProps> = ({ x, y, index }) => {
    if (x == null || y == null || index == null) return null;

    const dataItem = barData[index];
    if (!dataItem?.isToday) return null;

    return (
      <foreignObject x={x - 30} y={y - 40} width={150} height={40}>
        <div className="bg-white border-[gainsboro] px-2 py-1 rounded-full text-xs text-black shadow-sm">
          <span className="text-[turquoise] font-semibold">Today</span>: High
          spending
        </div>
      </foreignObject>
    );
  };

  interface CustomDotProps extends DotProps {
    payload: ChartData;
  }

  const CustomActiveDot: React.FC<CustomDotProps> = (props) => {
    const { cx, cy, payload } = props;

    const isMatch =
      typeof payload.expenses === "number" &&
      typeof payload.budget === "number" &&
      payload.expenses === payload.budget;

    return isMatch ? (
      <circle
        cx={cx}
        cy={cy}
        r={6}
        fill="#00C8C8"
        stroke="#fff"
        strokeWidth={2}
      />
    ) : null;
  };

  type CustomTooltipProps = TooltipProps<number, string> & {
    payload?: {
      payload: ChartData;
      value: number;
      dataKey: string;
      name: string;
      stroke: string;
    }[];
    label?: string;
    coordinate?: { x: number; y: number };
  };

  const CustomTooltip: React.FC<CustomTooltipProps> = ({
    active,
    payload,
    label,
    coordinate,
  }) => {
    if (active && payload && payload.length && coordinate) {
      const match = payload.find(
        (item) => item.payload.expenses === item.payload.budget
      );

      if (match) {
        const { x, y } = coordinate;

        return (
          <foreignObject
            x={x - 50}
            y={y - 70}
            width={100}
            height={60}
            className="pointer-events-none"
          >
            <div className="bg-gradient-to-r from-teal-400 to-cyan-400 text-white text-xs text-center rounded-lg shadow-md p-2">
              <p className="mb-1">{label}</p>
              <p className="text-sm font-bold">
                ${match.payload.expenses.toLocaleString()}
              </p>
            </div>
          </foreignObject>
        );
      }
    }

    return null;
  };

  return (
    <>
      <div className="min-h-screen bg-gray-50 p-4 lg:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Top Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {statCards.map((card, idx) => (
              <div
                key={idx}
                className="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1"
              >
                <div className="flex items-center justify-start mb-5">
                  <div className="pe-3">
                    <img
                      src={card.icon}
                      alt={`${card.title} Icon`}
                      className="w-[50px] h-[50px]"
                    />
                  </div>
                  <div>
                    <p className={`text-[${card.color}] font-semibold text-lg`}>
                      {card.title.replace(" ", "\n")}
                    </p>
                  </div>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>{card.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Trend Chart */}
            <div className="lg:col-span-2 bg-white rounded-xl p-6 shadow-sm">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <button
                    type="button"
                    title="View trend and forecast"
                    aria-label="View trend and forecast"
                    className="w-[80px] h-9 bg-blue-50 rounded-full flex items-center justify-center mr-3"
                  >
                    <FaDollarSign className="text-blue-900 mr-2" />
                  </button>
                  <h2 className="text-lg font-semibold text-[slategray]">
                    Trend and forecast
                  </h2>
                </div>
                <FaEllipsisH className="text-gray-400 cursor-pointer hover:text-gray-600 transition-colors" />
              </div>
              {/* charts   */}
              <div className=" ">
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={data}>
                    <CartesianGrid horizontal={false} vertical={false} />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      padding={{ left: 10, right: 10 }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      padding={{ top: 10, bottom: 10 }}
                      tickFormatter={(value) => `${value / 1000}k`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    {/* <Legend wrapperStyle={{ fontSize: "12px" }} /> */}
                    <Line
                      type="monotone"
                      dataKey="budget"
                      stroke="#E5E5E5"
                      strokeWidth={4}
                      dot={false}
                      activeDot={{ r: 5 }}
                      name="Budget"
                    />
                    <Line
                      type="monotone"
                      dataKey="expenses"
                      stroke="#2ADADA"
                      strokeWidth={4}
                      activeDot={(props) => <CustomActiveDot {...props} />}
                      dot={false}
                      name="Expenses"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Weekly chart */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <div className="text-center mb-6">
                <div className="text-2xl font-bold text-gray-800 mb-2">
                  Your weekly average is{" "}
                  <span className="text-gray-800">$100</span>
                </div>
              </div>

              <div className="relative overflow-visible">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={barData}
                    margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid
                      vertical={false}
                      horizontal={true}
                      strokeDasharray="5 5"
                    />
                    <XAxis
                      dataKey="day"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis
                      domain={[0, 70]}
                      ticks={[0, 50]}
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(val) => `$${val}`}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip
                      contentStyle={{
                        fontSize: "12px",
                        borderRadius: "6px",
                        padding: "8px",
                        zIndex: 50,
                        background: "white",
                      }}
                      wrapperStyle={{
                        overflow: "visible",
                      }}
                      formatter={(value) => `$${value}`}
                      labelFormatter={(label) => `Day ${label}`}
                    />
                    <ReferenceLine y={50} stroke="#999" strokeDasharray="5 5" />
                    <Bar
                      dataKey="amount"
                      fill="#037878"
                      radius={[999, 999, 999, 999]}
                      barSize={16}
                    >
                      <LabelList content={<CustomTodayLabel />} />
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-6">
                <button
                  onClick={() => setWeeklySpendModalOpen(true)}
                  className="w-full cursor-pointer bg-[darkturquoise] text-white py-3 rounded-lg font-medium hover:bg-[darkturquoise] transition-colors transform hover:scale-105 active:scale-95"
                >
                  Details
                </button>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
            {/* Anomaly */}
            <div className="bg-white rounded-xl p-3 pt-4 shadow-sm">
              <h3 className="text-lg font-semibold text-[slategray] mb-4">
                Anomaly
              </h3>
              <div className="flex items-start space-x-3">
                <div>
                  <img
                    src={unusualHighTransaction}
                    alt="warning Icon"
                    className="w-[31px] h-[29px]"
                  />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 mb-1">
                    Unusual High Transaction
                  </h4>
                  <p className="text-sm text-gray-600">
                    Your hotel claim on apr 10 is{" "}
                    <span className="font-semibold text-[goldenrod]">40%</span>{" "}
                    above the average
                  </p>
                </div>
              </div>
            </div>

            {/* Smart Recommendations */}
            <div className="bg-white rounded-xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-[slategray] mb-4">
                Smart Recommendations
              </h3>
              <div className="flex items-start space-x-3">
                <div>
                  <img
                    src={saveByBookingEarly}
                    alt="Idea Icon"
                    className="w-[31px] h-[31px]"
                  />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800 mb-1">
                    Save by booking early
                  </h4>
                  <p className="text-sm text-gray-600">
                    If you submit expenses{" "}
                    <span className="font-semibold text-[goldenrod]">7+</span>{" "}
                    days before, managers approve quicker
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {isWeeklySpendModalOpen && (
        <SpendingModal
          isOpen={isWeeklySpendModalOpen}
          onClose={() => setWeeklySpendModalOpen(false)}
        />
      )}
    </>
  );
};

export default MyInsights;
