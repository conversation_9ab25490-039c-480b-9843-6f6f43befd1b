import React, { useState } from "react";
import type { FC } from "react";
import ReceiptUpload from "./ReceiptUpload";

interface Expense {
  name: string;
  amount: number;
  category: string;
  description: string;
  receipts: { file: File; preview: string }[];
}

type ExpenseErrors = {
  [K in keyof Expense]?: string;
};

interface ExpenseModalProps {
  modalOpen: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  expenses: Expense[];
  setExpenses: React.Dispatch<React.SetStateAction<Expense[]>>;
  setIsSuccessModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const ExpenseModal: FC<ExpenseModalProps> = ({
  modalOpen,
  setModalOpen,
  expenses,
  setExpenses,
  setIsSuccessModalOpen,
}) => {
  const initialFormState: Expense = {
    name: "",
    amount: 0,
    category: "",
    description: "",
    receipts: [],
  };

  const [formData, setFormData] = useState<Expense>(initialFormState);
  const [formErrors, setFormErrors] = useState<ExpenseErrors>({});
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const categoryOptions = [
    "Travel",
    "Meals & Entertainment",
    "Supplies",
    "Lodging",
    "Miscellaneous",
  ];

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "amount" ? parseFloat(value) || 0 : value,
    }));

    // Clear error on change
    setFormErrors((prev) => ({
      ...prev,
      [name]: "",
    }));
  };

  const handleReceiptUpload = (files: File[]) => {
    const newReceipts = files.map((file) => ({
      file,
      preview: URL.createObjectURL(file),
    }));
    setFormData((prev) => ({
      ...prev,
      receipts: [...prev.receipts, ...newReceipts],
    }));
  };

  const handleRemoveReceipt = (indexToRemove: number) => {
    setFormData((prev) => {
      // Clean up the URL object to prevent memory leaks
      const receiptToRemove = prev.receipts[indexToRemove];
      if (receiptToRemove) {
        URL.revokeObjectURL(receiptToRemove.preview);
      }

      return {
        ...prev,
        receipts: prev.receipts.filter((_, index) => index !== indexToRemove),
      };
    });
  };

  const resetForm = () => {
    // Clean up URL objects before resetting
    formData.receipts.forEach((receipt) => {
      URL.revokeObjectURL(receipt.preview);
    });

    setFormData(initialFormState);
    setEditingIndex(null);
    setFormErrors({});
  };

  const validateForm = (): boolean => {
    const errors: ExpenseErrors = {};
    if (!formData.name.trim()) errors.name = "Expense name is required";
    if (!formData.category.trim()) errors.category = "Category is required";
    if (formData.amount <= 0) errors.amount = "Amount must be greater than 0";

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // If form has data, validate and add it
    if (formData.name || formData.category || formData.amount > 0) {
      if (!validateForm()) return;

      if (editingIndex !== null) {
        const updatedExpenses = [...expenses];
        updatedExpenses[editingIndex] = formData;
        setExpenses(updatedExpenses);
      } else {
        setExpenses((prev) => [...prev, formData]);
      }
    }

    // Check if we have any expenses to submit
    const totalExpenses =
      expenses.length +
      (formData.name || formData.category || formData.amount > 0 ? 1 : 0);
    if (totalExpenses === 0) {
      setFormErrors({
        name: "Please add at least one expense before submitting",
      });
      return;
    }

    resetForm();
    setModalOpen(false);
    setIsSuccessModalOpen(true);
  };

  const handleAddAnother = () => {
    if (!validateForm()) return;

    if (editingIndex !== null) {
      const updatedExpenses = [...expenses];
      updatedExpenses[editingIndex] = formData;
      setExpenses(updatedExpenses);
      setEditingIndex(null);
    } else {
      setExpenses((prev) => [...prev, formData]);
    }

    resetForm();
  };

  const handleRemoveExpense = (indexToRemove: number) => {
    const expenseToRemove = expenses[indexToRemove];
    // Clean up URL objects
    expenseToRemove.receipts.forEach((receipt) => {
      URL.revokeObjectURL(receipt.preview);
    });

    setExpenses((prev) => prev.filter((_, index) => index !== indexToRemove));
  };

  if (!modalOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-[rgba(0,0,0,0.66)] backdrop-blur-[6.5px] flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8 shadow-lg w-[800px] max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-[#1D3557]">
              Add New Expense
            </h2>
            <button
              className="text-red-500 cursor-pointer font-semibold hover:text-red-700"
              onClick={() => setModalOpen(false)}
            >
              Close
            </button>
          </div>

          {/* Display existing expenses in reverse order (most recent first) */}
          {[...expenses].reverse().map((expense, reverseIndex) => {
            const originalIndex = expenses.length - 1 - reverseIndex;
            // Hide this item while it's being edited
            if (originalIndex === editingIndex) return null;
            return (
              <div
                key={originalIndex}
                className="border rounded-lg p-4 mb-4 bg-gray-100 shadow-sm"
              >
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-semibold text-lg">{expense.name}</h4>
                  </div>
                  <div className="space-x-2">
                    <button
                      className="text-blue-500 hover:text-blue-700 bg-blue-100 hover:bg-blue-200 px-3 py-1 rounded-full text-sm"
                      onClick={() => {
                        setFormData(expense);
                        setEditingIndex(originalIndex);
                        setModalOpen(true);
                      }}
                    >
                      Edit
                    </button>

                    <button
                      className="text-red-500 hover:text-red-700 bg-red-100 hover:bg-red-200 px-3 py-1 rounded-full text-sm"
                      onClick={() => handleRemoveExpense(originalIndex)}
                    >
                      Remove
                    </button>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-[#1D3557] font-semibold text-md">
                    ₹ {expense.amount.toFixed(2)}
                  </span>
                  <span className="text-gray-600 text-sm bg-gray-200 px-2 py-1 rounded">
                    {expense.category}
                  </span>
                </div>

                <p className="text-gray-700 mt-2">{expense.description}</p>

                <div className="flex mt-2 space-x-2 items-center">
                  <span className="text-sm text-gray-600">
                    {expense.receipts.length} file
                    {expense.receipts.length !== 1 && "s"} uploaded
                  </span>
                  {expense.receipts.map((receipt, i) => (
                    <img
                      key={i}
                      src={receipt.preview}
                      alt="Receipt"
                      className="w-12 h-12 rounded-md object-cover border"
                    />
                  ))}
                </div>
              </div>
            );
          })}

          {/* Form for adding new expense */}
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <label
                  htmlFor="name"
                  className="block text-[#0F1417] font-medium text-sm mb-2"
                >
                  Expense Name
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full border border-gray-300 bg-[#F9FAFB] rounded-lg px-3 py-2 text-[#0F1417] placeholder:text-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#D4DBE3] focus:border-transparent transition"
                  placeholder="Enter expense name"
                />
                {formErrors.name && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>
                )}
              </div>
              <div>
                <label
                  htmlFor="category"
                  className="block text-[#0F1417] font-medium text-sm mb-2"
                >
                  Select Category
                </label>
                <div className="relative">
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    className="appearance-none w-full border border-[#D1D5DB] rounded-xl px-4 py-3 text-[#0F1417] placeholder-[#A3AEB8] bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#D4DBE3] focus:border-transparent transition-all"
                  >
                    <option
                      value=""
                      disabled
                      hidden
                      className="text-[#0F1417] placeholder-[#A3AEB8]"
                    >
                      Select Category
                    </option>
                    {categoryOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>

                  {/* Custom dropdown icon */}
                  <div className="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-400">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </div>
                </div>

                {formErrors.category && (
                  <p className="text-red-500 text-sm mt-1">
                    {formErrors.category}
                  </p>
                )}
              </div>
              <div>
                <div>
                  <label
                    htmlFor="amount"
                    className="block text-[#0F1417] font-medium text-sm mb-2"
                  >
                    Claim Amount
                  </label>
                  <input
                    id="amount"
                    name="amount"
                    type="number"
                    step="0.01"
                    value={formData.amount || ""}
                    onChange={handleChange}
                    className="w-full mb-2 border border-gray-300 bg-[#F9FAFB] rounded-lg px-3 py-2 text-gray-500 placeholder:text-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#D4DBE3] focus:border-transparent transition"
                    placeholder="Enter Amount"
                  />
                  {formErrors.amount && (
                    <p className="text-red-500 text-sm mt-1">
                      {formErrors.amount}
                    </p>
                  )}
                </div>
                <div>
                  <ReceiptUpload
                    receipts={formData.receipts}
                    onUpload={handleReceiptUpload}
                    onRemove={handleRemoveReceipt}
                  />
                </div>
              </div>
              <div>
                <div>
                  <label
                    htmlFor="description"
                    className="block text-[#0F1417] font-medium text-sm mb-2"
                  >
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    className="w-full border border-gray-300 bg-[#F9FAFB] rounded-lg px-3 py-2 text-gray-500 placeholder:text-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#D4DBE3] focus:border-transparent transition"
                    placeholder="Enter Description"
                    rows={6}
                  />
                </div>
                <div className="flex justify-between mt-6">
                  <button
                    type="button"
                    onClick={handleAddAnother}
                    className="bg-[#4FD1C7] text-white px-6 py-2 rounded-lg hover:bg-[#38B2AC] transition-colors"
                  >
                    + Add Another Expense
                  </button>
                  <button
                    type="submit"
                    className="bg-[#4A5568] text-white px-8 py-2 rounded-lg hover:bg-[#2D3748] transition-colors"
                  >
                    Submit
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default ExpenseModal;
