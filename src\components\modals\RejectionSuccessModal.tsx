import expenceRejected from "../../components/icons/expenceRejected.gif";
import React from "react";

interface RejectionSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBackToHome: () => void;
  onUndoRejection: () => void;
}

const RejectionSuccessModal: React.FC<RejectionSuccessModalProps> = ({
  isOpen,
  onClose,
  onBackToHome,
  onUndoRejection,
}) => {
  if (!isOpen) return null;
  

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl w-full max-w-md mx-4 p-8 text-center">
          {/* Header */}
          <div className="flex justify-end mb-4">
            <button
              onClick={onClose}
              className="text-red-500 hover:text-red-600 font-medium"
            >
              Close
            </button>
          </div>
  
          {/* Success Icon and Clipboard Illustration */}
          <div className="mx-auto w-full max-w-[200px]">
          <img
            src={expenceRejected}
            alt="successfully raised gif"
            className="w-full h-auto object-contain"
          />
        </div>
  
          {/* Success Message */}
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Expense Approved!</h2>
          <p className="text-gray-500 mb-8">
            You have successfully approved the expense report of <br />
            <span className="font-medium text-gray-700">employee name</span>
          </p>
  
          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              onClick={onUndoRejection}
              className="flex-1 bg-gray-200 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-300 transition-colors"
            >
              Undo Rejection
            </button>
            <button
              onClick={onBackToHome}
              className="flex-1 bg-gray-800 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-900 transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
      </div>
  );
};

export default RejectionSuccessModal;
