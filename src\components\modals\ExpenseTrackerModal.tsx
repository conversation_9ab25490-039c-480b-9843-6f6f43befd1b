import React, { useState } from "react";
import { DollarSign } from "lucide-react";
import expenseTrackerSuccessIcon from "../icons/expenseTrackerSuccessIcon.svg";
import expenseTrackerRejectionIcon from "../icons/expenseTrackerRejectionIcon.svg";
import expenseTrackerPendingIcon from "../icons/expenseTrackerPendingIcon.svg";
import { FaFile } from "react-icons/fa6";
import { IoImageOutline } from "react-icons/io5";
import type {
  ApprovalStatus,
  SelectedExpenseReport,
} from "../../types/expenseTypes";
import RaiseDisputeModal from "./RaiseDisputeModal";
import ModalImage from "react-modal-image";

interface ExpenseTrackerModalProps {
  isOpenTrackDisputeModal: boolean;
  handleCloseTackerDispute: () => void;
  selectedExpenseReport: SelectedExpenseReport | null;
}

const ExpenseTrackerModal: React.FC<ExpenseTrackerModalProps> = ({
  isOpenTrackDisputeModal,
  handleCloseTackerDispute,
  selectedExpenseReport,
}) => {
  const [isRaiseDisputeModalOpen, setIsRaiseDisputeModalOpen] =
    useState<boolean>(false);

  if (!selectedExpenseReport) return null;

  // Function to get the appropriate icon based on approval status
  const getApprovalIcon = (status: ApprovalStatus) => {
    switch (status) {
      case "Approved":
        return expenseTrackerSuccessIcon;
      case "Rejected":
        return expenseTrackerRejectionIcon;
      case "Pending":
        return expenseTrackerPendingIcon;
      default:
        return expenseTrackerPendingIcon;
    }
  };

  const totalAmount = selectedExpenseReport.expenses.reduce(
    (sum, expense) => sum + expense.amount,
    0
  );
  const isDisputable = selectedExpenseReport.reportApprovals.some(
    (approval) =>
      approval.status === "Rejected" &&
      (approval.stage === "Finance" ||
        approval.stage === "Payment" ||
        approval.stage === "Manager")
  );
  console.log(selectedExpenseReport, "selectedExpenseReport");
  return (
    <>
      {isOpenTrackDisputeModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center z-50 p-4 animate-fadeIn duration-300">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto transform animate-slideIn transition-all duration-300 animate-in zoom-in-95 shadow-2xl scrollbar-hide">
            {/* Header */}
            <div className="flex items-center justify-between p-6 pb-4">
              <div>
                <h2 className="text-2xl font-bold text-[darkslateblue]">
                  Track Expense
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  {selectedExpenseReport.reportTitle} - Report #
                  {selectedExpenseReport.reportId}
                </p>
              </div>
              <button
                onClick={handleCloseTackerDispute}
                className="text-red-500 cursor-pointer hover:text-red-600 transition-colors duration-200 text-sm font-medium"
              >
                Close
              </button>
            </div>

            <div className="px-6 pb-6">
              {/* Approval Timeline */}
              <div className="flex items-center justify-between mb-6 bg-[white] shadow-lg rounded-xl p-4">
                {selectedExpenseReport.reportApprovals.map(
                  (approval, index) => (
                    <React.Fragment key={approval.stage}>
                      {/* Approval Step */}
                      <div className="flex flex-col items-center">
                        <span className="text-xs text-gray-600 mb-2">
                          {approval.stage === "Payment"
                            ? "Payment Release"
                            : approval.stage}
                        </span>
                        <img
                          src={getApprovalIcon(approval.status)}
                          alt={`${approval.stage} ${approval.status}`}
                          className="w-8 h-8"
                        />
                      </div>

                      {/* Connection Line */}
                      {index <
                        selectedExpenseReport.reportApprovals.length - 1 && (
                        <div className="flex-1 mx-2 relative h-8">
                          <div
                            className={`absolute top-1/2 -translate-y-1/2 w-full border-t-3 border-dashed ${
                              approval.status === "Approved" &&
                              selectedExpenseReport.reportApprovals[index + 1]
                                ?.status !== "Rejected"
                                ? "border-teal-500"
                                : "border-gray-400"
                            }`}
                          ></div>
                        </div>
                      )}
                    </React.Fragment>
                  )
                )}
              </div>

              {/* Claim Total */}
              <div className="bg-[teal] text-white p-4 rounded-lg flex items-center justify-between mb-6">
                <span className="font-medium">Claim Total</span>
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4" />
                  <span className="text-lg font-bold">
                    {totalAmount.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            {/* Expense Items */}
            <div className={`px-6 space-y-4 ${!isDisputable && "mb-6"}`}>
              {selectedExpenseReport.expenses.length === 0 ? (
                <div className="text-center py-8 text-gray-500  bg-[white] shadow-lg rounded-xl p-4">
                  No expenses found in this report.
                </div>
              ) : (
                selectedExpenseReport.expenses.map((expense) => (
                  <div
                    key={expense.id}
                    className=" bg-[white] shadow-lg rounded-xl p-4"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold text-[slategray] text-lg">
                          {expense.title}
                        </h3>
                        <p className="text-xs text-[darkgray] mt-1">
                          {expense.date}
                        </p>
                      </div>
                      <span
                        className={`text-xs font-medium px-8 py-2 rounded-full text-white ${
                          expense.status === "Approved"
                            ? "bg-[limegreen]"
                            : expense.status === "Rejected"
                            ? "bg-[tomato]"
                            : "bg-[darkorange] "
                        }`}
                      >
                        {expense.status}
                      </span>
                    </div>

                    <div className="flex items-center space-x-6 mb-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-[darkslateblue] font-medium">
                          {expense.amount.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {/* <div className="w-4 h-4 bg-gray-400 rounded-sm flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-sm"></div>
                        </div> */}
                        <span className="text-sm text-[darkslateblue]">
                          {expense.category}
                        </span>
                      </div>
                    </div>

                    <p className="text-sm text-gray-500 mb-4 leading-relaxed">
                      {expense.description}
                    </p>

                    <div className="flex items-center justify-start space-x-4 mb-2">
                      <div className="flex items-center space-x-2">
                        <FaFile className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-[darkslategray]">
                          {expense.filesUploaded} files uploaded
                        </span>
                      </div>

                      <div className="flex space-x-2">
                        {expense.attachments
                          .slice(0, 3)
                          .map((attachment, idx) => (
                            <PreviewImage
                              key={idx}
                              src={attachment}
                              alt={`Attachment ${idx + 1}`}
                              idx={idx}
                            />
                          ))}

                        {expense.attachments.length > 3 && (
                          <div className="w-8 h-8 bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                            <span className="text-xs text-gray-600">
                              +{expense.attachments.length - 3}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Footer */}
            {isDisputable && (
              <div className="p-6">
                <div className="flex justify-end space-x-3">
                  <button
                    disabled={!isDisputable}
                    onClick={() => setIsRaiseDisputeModalOpen(true)}
                    className="cursor-pointer bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-12 rounded-lg transition-all duration-200 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
                  >
                    Raise Dispute
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      {isRaiseDisputeModalOpen && (
        <RaiseDisputeModal
          isRaiseDisputeModalOpen={isRaiseDisputeModalOpen}
          handleCloseRaiseDisputeModal={() => setIsRaiseDisputeModalOpen(false)}
          handleCloseTackerDispute={handleCloseTackerDispute}
        />
      )}
    </>
  );
};

export default ExpenseTrackerModal;

interface AttachmentProps {
  src: string;
  alt: string;
  idx: number;
}

const PreviewImage: React.FC<AttachmentProps> = ({ src, alt, idx }) => {
  const [error, setError] = useState(false);

  return (
    <div key={idx} className="w-12 h-12 rounded overflow-hidden">
      {error ? (
        <div className="w-[60px] h-[45px] bg-gray-100 flex items-center justify-center text-gray-400 border border-gray-200 rounded">
          <IoImageOutline className="w-6 h-6" />
        </div>
      ) : (
        <ModalImage
          small={src}
          large={src}
          alt={alt}
          hideDownload={true}
          hideZoom={true}
          className="object-cover w-[60px] h-[45px] cursor-pointer"
          onError={() => setError(true)}
        />
      )}
    </div>
  );
};
