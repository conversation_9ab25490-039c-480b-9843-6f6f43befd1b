export type ApprovalStatus = "Pending" | "Approved" | "Rejected";

export interface ApprovalStep {
    stage: "Manager" | "Finance" | "Payment";
    status: "Pending" | "Approved" | "Rejected";
}
export interface Approval {
    stage: "Manager" | "Finance" | "Payment";
    status: "Approved" | "Rejected" | "Pending" | "In Review";
}

export interface Attachment {
    name?: string;
    url: string;
}

export interface ExpenseItem {
    id: string;
    title: string;
    amount: number;
    category: string;
    description: string;
    date: string;
    status: "Pending" | "Approved" | "Rejected";
    approvals: ApprovalStep[];
    filesUploaded: number;
    attachments: string[];
}

export interface Expense {
    id: number;
    title: string;
    description: string;
    category: string;
    submissionDate: string;
    amount: string;
    usageLimit: string;
    approvals: ApprovalStep[];
    status: "Pending" | "Approved" | "Rejected";
    expenses: ExpenseItem[];
}
export interface SelectedExpenseReport {
    reportId: number;
    reportTitle: string;
    reportAmount: string;
    reportStatus: "Pending" | "Approved" | "Rejected";
    reportApprovals: ApprovalStep[];
    expenses: ExpenseItem[];
}

export interface DisputeData {
    id: string | number;
    title: string;
    description: string;
    category: string;
    submissionDate: string;
    amount: string;
    usageLimit?: string;
    approvals: Approval[];
    status: "In Review" | "Approved" | "Rejected" | "Pending";
    disputeReason?: string;
    date?: string;
    currency?: string;
    attachedEvidence?: Attachment[];
    expenses?: ExpenseItem[];
}