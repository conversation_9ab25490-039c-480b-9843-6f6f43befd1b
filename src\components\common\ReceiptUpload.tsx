import { useState } from "react";
interface ReceiptUploadProps {
  receipts: { file: File; preview: string }[];
  onUpload: (files: File[]) => void;
  onRemove: (index: number) => void;
}

const ReceiptUpload: React.FC<ReceiptUploadProps> = ({
  receipts,
  onUpload,
  onRemove,
}) => {
  const [previewModal, setPreviewModal] = useState<{
    open: boolean;
    file: string | null;
  }>({
    open: false,
    file: null,
  });

  const [isDragActive, setIsDragActive] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onUpload(files);
    }
  };
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      onUpload(Array.from(files));
    }
  };
  return (
    <>
      <label className="block text-[#0F1417] font-medium text-sm mb-2">
        Upload Receipt
      </label>

      {/* Dropzone
      <div
        {...getRootProps()}
        className={`border-dashed border-2 rounded-lg p-6 py-9 text-center cursor-pointer ${
          isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
        }`}
      >
        <input {...getInputProps()} />
        <p className="text-gray-500 mb-4">
          {isDragActive ? 'Drop the files here...' : 'Drag and drop or click to upload'}
        </p>
        <div className="bg-[#4A5568] text-white px-6 py-2 rounded-lg inline-block">
          Upload
        </div>
      </div> */}
      {/* Dropzone */}
      <div
        className={`border-dashed border-2 rounded-lg p-6 py-9 text-center cursor-pointer transition-colors ${
          isDragActive ? "border-blue-400 bg-blue-50" : "border-gray-300"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => document.getElementById("receipt-upload")?.click()}
      >
        <input
          id="receipt-upload"
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInputChange}
          className="hidden"
        />
        <p className="text-gray-500 mb-4">
          {isDragActive
            ? "Drop the files here..."
            : "Drag and drop or click to upload"}
        </p>
        <label
          htmlFor="receipt-upload"
          className="bg-[#4A5568] text-white px-6 py-2 rounded-lg inline-block hover:bg-[#2D3748] transition-colors cursor-pointer"
          title="Upload receipt"
        >
          Upload
        </label>
      </div>

      {/* Preview
      {formData.receipts.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-4">
          {formData.receipts.map((receipt, i) => (
            <div key={i} className="relative w-16 h-16">
              <img
                src={receipt}
                alt="Receipt"
                className="w-full h-full rounded-md object-cover border cursor-pointer"
                onClick={() => setPreviewModal({ open: true, file: receipt })}
              />
              <button
                type="button"
                onClick={() => handleRemoveReceipt(i)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shadow-md hover:bg-red-600"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )} */}
      {/* Preview */}
      {receipts.length > 0 && (
        <div className="mt-4 flex flex-wrap gap-4">
          {receipts.map((receipt, i) => (
            <div key={i} className="relative w-16 h-16">
              <img
                src={receipt.preview}
                alt="Receipt"
                className="w-full h-full rounded-md object-cover border cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() =>
                  setPreviewModal({ open: true, file: receipt.preview })
                }
              />
              <button
                type="button"
                onClick={() => onRemove(i)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shadow-md hover:bg-red-600 transition-colors"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Modal
      {previewModal.open && previewModal.file && (
        <div className="fixed inset-0 bg-[rgba(0,0,0,0.66)] backdrop-blur-[6.5px] flex justify-center items-center z-50">
          <div className="relative bg-white p-4 rounded shadow-lg max-w-full max-h-full">
            <img
              src={previewModal.file}
              alt="Full Preview"
              className="max-w-[90vw] max-h-[80vh] object-contain"
            />
            <button
              onClick={() => setPreviewModal({ open: false, file: null })}
              className="absolute top-1 right-1 bg-gray-800 text-white rounded-full px-3 py-1 hover:bg-gray-600"
            >
              ✕
            </button>
          </div>
        </div>
      )} */}
      {/* Preview Modal */}
      {previewModal.open && previewModal.file && (
        <div className="fixed inset-0 bg-[rgba(0,0,0,0.66)] backdrop-blur-[6.5px] flex justify-center items-center z-50">
          <div className="relative bg-white p-4 rounded shadow-lg max-w-full max-h-full">
            <img
              src={previewModal.file}
              alt="Full Preview"
              className="max-w-[90vw] max-h-[80vh] object-contain"
            />
            <button
              onClick={() => setPreviewModal({ open: false, file: null })}
              className="absolute top-1 right-1 bg-gray-800 text-white rounded-full px-3 py-1 hover:bg-gray-600 transition-colors"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ReceiptUpload;
