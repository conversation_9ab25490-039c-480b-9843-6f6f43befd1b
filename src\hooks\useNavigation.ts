import { useNavigate, useRouter } from '@tanstack/react-router';

export const useAppNavigation = () => {
    // These hooks must be called inside a component that's within the Router context
    const navigate = useNavigate();
    const router = useRouter();

    const navigateTo = {
        home: () => navigate({ to: '/' }),
        login: () => navigate({ to: '/auth/login' }),
        insights: () => navigate({ to: '/insights' }),
        notifications: () => navigate({ to: '/notifications' }),
        finance: () => navigate({ to: '/finance' }),
        management: () => navigate({ to: '/management' }),
        profile: () => navigate({ to: '/profile' }),
        accountInfo: () => navigate({ to: '/profile/account-information' }),
        privacyPolicy: () => navigate({ to: '/profile/privacy-policy' }),
        support: () => navigate({ to: '/profile/support' }),
        unauthorized: () => navigate({ to: '/unauthorized' }),
    };

    const goBack = () => {
        // Use router.history for better integration
        if (router.history.length > 1) {
            router.history.back();
        } else {
            navigate({ to: '/' });
        }
    };

    return {
        navigate,
        navigateTo,
        goBack,
        router,
    };
};