import pageNotFound from "../../components/icons/404.svg";
const NotFound = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 py-8">
      <div className="max-w-md w-full text-center">
        {/* 404 Image Container */}
        <div className="mb-8 flex justify-center">
          <div className="w-screen h-screen sm:w-80 sm:h-80 md:w-96 md:h-96 flex items-center justify-center">
            {/* Replace this div with your actual image */}
            <img
              src={pageNotFound}
              alt={"404"}
              className="max-w-screen max-h-screen object-contain"
              onError={(e) => {
                // Fallback if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.nextElementSibling?.classList.remove("hidden");
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
