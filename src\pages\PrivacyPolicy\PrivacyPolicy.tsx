import React from "react";
import { FaChevronLeft } from "react-icons/fa";
import { useRouter } from "@tanstack/react-router";

// Types
interface PrivacyPolicyProps {
  onBack?: () => void;
}

interface PolicySection {
  id: number;
  title: string;
  content: string;
}

const policyData: PolicySection[] = [
  {
    id: 1,
    title: "Data Collection & Use",
    content:
      "We collect the information you provide when you register and use the app — such as your name, email address, department, role, and bank details for reimbursements — as well as any expense data you submit (dates, amounts, categories, receipts, descriptions). We use this data to authenticate you, process and track your expense claims, generate personalized insights and notifications, and facilitate approvals and payments. We also gather anonymized usage metrics (e.g., pages viewed, button taps) to improve the user experience and application performance.",
  },
  {
    id: 2,
    title: "Data Sharing & Third-Parties",
    content:
      "Your personal and expense information is only shared with authorized parties involved in the reimbursement workflow: your manager, finance team, and any payment processor you designate. We do not sell or rent your data to external marketing firms. We may share aggregated, non-identifiable data with analytics providers or auditors to help us refine our service and maintain compliance, but this summary data cannot be traced back to any individual.",
  },
  {
    id: 3,
    title: "Security & Storage",
    content:
      "We store your data on secure, industry-standard servers with encryption both at rest and in transit (TLS). Access to production systems is strictly limited to authorized personnel under multi-factor authentication. We perform regular security audits and vulnerability scans to ensure data integrity and protect against unauthorized access, and we follow best practices (e.g., least-privilege access, secure coding) to keep your information safe.",
  },
  {
    id: 4,
    title: "Your Rights & Choices",
    content:
      "You have the right to access, correct, or delete your personal information at any time via your Profile settings or by contacting our support team. You can also export your expense and activity history in common formats (CSV/PDF). If you choose to disable certain notifications or purge historical data, the system will honor these requests in accordance with applicable data-retention policies.",
  },
  {
    id: 5,
    title: "Retention & Compliance",
    content:
      "We retain your expense records and audit logs only as long as necessary to fulfill tax, legal, or business obligations—typically up to seven years unless your organization's policy dictates otherwise. We comply with relevant privacy laws (e.g., GDPR, CCPA) and will notify you of any material changes to this policy. For full details, please review our comprehensive Privacy Policy document <NAME_EMAIL>.",
  },
  {
    id: 6,
    title: "Cookies & Tracking Technologies",
    content:
      "We use essential and performance cookies to ensure smooth app operation, analyze traffic, and enhance functionality. These cookies do not collect personally identifiable information unless explicitly provided by you during interactions such as logins or form submissions. You can manage cookie preferences through your browser settings.",
  },
  {
    id: 7,
    title: "Third-Party Integrations",
    content:
      "Our application may offer optional integrations with third-party services like calendar tools or accounting platforms. When enabled, we only access the minimum required data to facilitate the feature. All third-party access is governed by their respective privacy policies and security practices.",
  },
  {
    id: 8,
    title: "Children’s Privacy",
    content:
      "This application is not intended for use by individuals under the age of 16. We do not knowingly collect personal information from children. If we become aware that we have inadvertently received such data, we will promptly delete it from our systems.",
  },
  {
    id: 9,
    title: "Policy Updates",
    content:
      "We may revise this Privacy Policy periodically to reflect new features, legal requirements, or improvements in our practices. When changes are made, we’ll notify you via email or in-app alerts and indicate the updated effective date at the top of the policy.",
  },
  {
    id: 10,
    title: "Contact & Dispute Resolution",
    content:
      "If you have any concerns or questions about your privacy or how your data is handled, please contact <NAME_EMAIL>. We aim to resolve all complaints promptly and fairly. In case of unresolved issues, users within certain jurisdictions may escalate matters to the appropriate regulatory authority.",
  },
];

// Main Privacy Policy Component
const PrivacyPolicy: React.FC<PrivacyPolicyProps> = ({ onBack }) => {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.navigate({ to: "/profile" });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 md:p-6 lg:p-8">
      <div className="max-w-5xl mx-auto bg-white shadow-lg rounded-lg">
        {/* Header */}
        <div className="p-6 md:p-8 border-gray-200">
          <button
            onClick={handleBack}
            className="flex items-center cursor-pointer text-teal-600 hover:text-teal-700 mb-6 transition-all duration-300 hover:scale-105"
            aria-label="Go back"
          >
            <FaChevronLeft className="w-4 h-4 mr-2" />
            <span className="text-sm font-medium">Back</span>
          </button>
          <h1 className="text-2xl md:text-3xl font-bold text-[slategray] mb-2">
            Privacy Policy
          </h1>
        </div>

        {/* Content */}
        <div className="p-6 md:p-8">
          <div className="space-y-8">
            {policyData.map((section) => (
              <div key={section.id} className="animate-fadeIn">
                <h2 className="text-lg md:text-xl font-semibold text-[darkturquoise] mb-4 flex items-start">
                  <span className="text-[darkturquoise] mr-2">
                    {section.id}.
                  </span>
                  {section.title}
                </h2>
                <div className="ml-6 md:ml-8">
                  <p className="text-[slategray] leading-relaxed text-sm md:text-base">
                    {section.content}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <p className="text-xs md:text-sm text-gray-500 text-center">
              Last updated:{" "}
              {new Date().toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
