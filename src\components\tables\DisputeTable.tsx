import React, { useState } from "react";
import { FaCheckCircle } from "react-icons/fa";
import ButtonGroup from "../ButtonGroup";

import TrackDisputeModal from "../modals/TrackDisputeModal";
import { disputeData } from "../../db/expenseData";
import type { DisputeData } from "../../types/expenseTypes";

const TIME_FILTERS = ["12 months", "30 days", "7 days", "24 hours"] as const;
type TimeFilter = (typeof TIME_FILTERS)[number];

const DisputeTable: React.FC = () => {
  const [selectedTimeFilter, setSelectedTimeFilter] =
    useState<TimeFilter>("12 months");
  const [isTrackDisputeModalOpen, setIsTrackDisputeModalOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<DisputeData>(
    disputeData[0]
  );

  const isWithinTimeRange = (
    dateStr: string,
    timeRange: TimeFilter
  ): boolean => {
    const submissionDate = new Date(dateStr);
    const now = new Date();

    switch (timeRange) {
      case "24 hours":
        return now.getTime() - submissionDate.getTime() <= 24 * 60 * 60 * 1000;
      case "7 days":
        return (
          now.getTime() - submissionDate.getTime() <= 7 * 24 * 60 * 60 * 1000
        );
      case "30 days":
        return (
          now.getTime() - submissionDate.getTime() <= 30 * 24 * 60 * 60 * 1000
        );
      case "12 months":
        return (
          now.getTime() - submissionDate.getTime() <= 365 * 24 * 60 * 60 * 1000
        );
      default:
        return true;
    }
  };

  const filteredExpenses = disputeData.filter((expense) =>
    isWithinTimeRange(expense.submissionDate, selectedTimeFilter)
  );

  const handleViewClick = (expense: DisputeData) => {
    setSelectedExpense(expense);
    setIsTrackDisputeModalOpen(true);
  };

  return (
    <>
      <div className="p-6 bg-white rounded-lg max-w-full overflow-x-hidden">
        <div className="mb-6 flex flex-wrap items-center justify-between gap-3">
          <h2 className="text-sky-900 font-bold text-md">Active Disputes</h2>

          <div className="flex flex-wrap items-center gap-3">
            <input
              type="text"
              className="border border-gray-300 rounded-sm py-1.5 px-2 text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 min-w-[100px]"
              aria-label="Filter by date"
              placeholder="Date"
            />

            <div className="flex flex-col sm:flex-row gap-2">
              <ButtonGroup
                options={TIME_FILTERS}
                selected={selectedTimeFilter}
                onChange={setSelectedTimeFilter}
                className="cursor-pointer rounded-2xl"
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-hidden max-h-[500px] overflow-y-auto">
          <table className="min-w-full text-left border-collapse text-md">
            <thead>
              <tr className="border-b border-gray-200 text-gray-700 font-medium text-sm">
                <th className="py-3 px-4">Expense Report</th>
                <th className="py-3 px-4">Category</th>
                <th className="py-3 px-4">Dispute Raised On</th>
                <th className="py-3 px-4">Amount</th>
                <th className="py-3 px-4">Rejected By</th>
                <th className="py-3 px-4">Rejection Reason</th>
                <th className="py-3 px-4">Status</th>
                <th className="py-3 px-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredExpenses.length === 0 ? (
                <tr>
                  <td colSpan={8} className="text-center py-10 text-gray-500">
                    No expenses found for the selected filters.
                  </td>
                </tr>
              ) : (
                filteredExpenses.map((expense) => (
                  <tr
                    key={expense.id}
                    className="border-b border-gray-100 hover:bg-gray-50 transition-colors text-lg"
                  >
                    <td className="py-4 text-sm px-4 break-words align-top text-gray-800 whitespace-nowrap">
                      {expense.title}
                    </td>

                    {/* <td
                      className="text-sm py-4 px-4 align-top font-semibold text-gray-600 max-w-xs truncate"
                      title={expense.description}
                    >
                      {expense.description.length > 35
                        ? expense.description.substring(0, 35) + "…"
                        : expense.description}
                    </td> */}

                    <td className="text-sm py-4 font-semibold px-4 align-top text-gray-500 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <img
                          src="categoryIcon.svg"
                          alt=""
                          className="text-gray-600 w-4 h-4"
                        />
                        <span>{expense.category}</span>
                      </div>
                    </td>

                    <td className="text-sm py-4 px-4 align-top font-semibold text-gray-500 cursor-pointer whitespace-nowrap">
                      {expense.submissionDate}
                    </td>

                    <td className="text-sm py-4 px-4 align-top text-gray-500 font-bold whitespace-nowrap">
                      {expense.amount}
                    </td>

                    <td className="text-sm py-4 px-4 font-semibold align-top text-gray-500 whitespace-nowrap">
                      {expense.approvals.find((a) => a.status === "Rejected")
                        ?.stage || "-"}
                    </td>
                    <td className="text-sm py-4 px-4 font-semibold align-top text-gray-500 whitespace-nowrap">
                      Missing receipt
                    </td>

                    <td className="py-2 px-1 align-top">
                      <div className="flex gap-1 bg-gray-100 rounded-2xl items-center justify-center p-2">
                        {expense.approvals.map((approval, idx) => (
                          <React.Fragment key={idx}>
                            <FaCheckCircle
                              className={`rounded-full ${
                                approval.status === "Approved"
                                  ? "text-teal-500 bg-green-100"
                                  : approval.status === "Rejected"
                                  ? "text-red-500 bg-red-100"
                                  : "text-gray-400"
                              }`}
                              size={20}
                              title={`${approval.stage}: ${approval.status}`}
                            />
                            {idx < expense.approvals.length - 1 && (
                              <div className="w-[1px] min-h-6 bg-gradient-to-b from-transparent via-cyan-900 to-transparent mx-1" />
                            )}
                          </React.Fragment>
                        ))}
                      </div>
                    </td>

                    <td className="py-4 px-4 align-top text-sm text-gray-500 font-bold cursor-pointer whitespace-nowrap hover:underline select-none">
                      <button
                        className="cursor-pointer hover:text-blue-600 transition-colors"
                        onClick={() => handleViewClick(expense)}
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      <TrackDisputeModal
        isTrackDisputeModalOpen={isTrackDisputeModalOpen}
        handleCloseTrackDisputeModal={() => setIsTrackDisputeModalOpen(false)}
        selectedExpense={selectedExpense}
      />
    </>
  );
};

export default DisputeTable;
