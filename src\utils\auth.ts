
import type { User } from "../types/auth"
import { UserRole } from "../types/auth"


export class AuthUtils {
    // Check if user has a specific role
    static hasRole(user: User | null, role: UserRole): boolean {
        if (!user) return false
        return user.role === role
    }

    // Check if user has any of the specified roles
    static hasAnyRole(user: User | null, roles: UserRole[]): boolean {
        if (!user) return false
        return roles.includes(user.role)
    }

    // Check if user can access a specific route based on role
    static canAccessRoute(user: User | null, routePath: string): boolean {
        if (!user) return false

        // Define route access rules based on roles
        const routeAccess: Record<string, UserRole[]> = {
            '/': [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
            '/insights': [UserRole.MANAGER, UserRole.FINANCE],
            '/notifications': [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
            '/profile': [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
            '/profile/account-information': [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
            '/profile/privacy-policy': [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
            '/profile/support': [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
            '/finance': [UserRole.FINANCE],
            '/management': [UserRole.MANAGER],
            '/reports': [UserRole.FINANCE],
        }

        const allowedRoles = routeAccess[routePath]
        if (!allowedRoles) return true // Public route

        return allowedRoles.includes(user.role)
    }
}