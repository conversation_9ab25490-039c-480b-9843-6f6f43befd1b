import React, { useState } from "react";
import {
  FaFileAlt, // FileText
  FaCheckCircle, // Approval
  FaTimesCircle, // Rejection
  FaExclamationTriangle, // Budget warning
  FaBalanceScale, // Dispute submitted
  FaCheck, // Dispute resolved
  FaInbox, // Empty state
} from "react-icons/fa";

interface Notification {
  id: number;
  type:
    | "expense_submitted"
    | "approval"
    | "rejection"
    | "budget_warning"
    | "dispute_submitted"
    | "dispute_resolved";
  title: string;
  description: string;
  time: string;
  category: string;
  isRead?: boolean;
}

const Notifications: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<string>("All");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Dummy data for notifications
  const notifications: Notification[] = [
    {
      id: 1,
      type: "expense_submitted",
      title: "Expense Submitted",
      description:
        'Your expense "Taxi Ride" has been successfully submitted and is now Pending approval',
      time: "4m ago",
      category: "all",
      isRead: false,
    },
    {
      id: 2,
      type: "approval",
      title: "Approval",
      description:
        'Congratulations! Your "Hotel Stay" claim has been Approved.',
      time: "4m ago",
      category: "approvals",
      isRead: false,
    },
    {
      id: 3,
      type: "rejection",
      title: "Rejection",
      description:
        'Heads up: Your "Client Dinner" expense was Rejected. Tap to view the manager\'s feedback.',
      time: "10m ago",
      category: "rejections",
      isRead: true,
    },
    {
      id: 4,
      type: "budget_warning",
      title: "Budget Warning",
      description: "You've used 80% of your monthly Travel budget",
      time: "1 day ago",
      category: "escalations",
      isRead: false,
    },
    {
      id: 5,
      type: "dispute_submitted",
      title: "Dispute Submitted",
      description: 'Your dispute for "Client Dinner" has been sent to Finance.',
      time: "2 days ago",
      category: "escalations",
      isRead: true,
    },
    {
      id: 6,
      type: "dispute_resolved",
      title: "Dispute Resolved",
      description:
        'Your dispute on "Taxi Ride" has been approved - see details.',
      time: "Just now",
      category: "all",
      isRead: false,
    },
  ];

  const getIcon = (type: Notification["type"]) => {
    const iconClass = "w-4 h-4 sm:w-5 sm:h-5";
    switch (type) {
      case "expense_submitted":
        return <FaFileAlt className={`${iconClass} text-blue-500`} />;
      case "approval":
        return <FaCheckCircle className={`${iconClass} text-green-500`} />;
      case "rejection":
        return <FaTimesCircle className={`${iconClass} text-red-500`} />;
      case "budget_warning":
        return (
          <FaExclamationTriangle className={`${iconClass} text-orange-500`} />
        );
      case "dispute_submitted":
        return <FaBalanceScale className={`${iconClass} text-orange-500`} />;
      case "dispute_resolved":
        return <FaCheck className={`${iconClass} text-green-500`} />;
      default:
        return <FaFileAlt className={`${iconClass} text-gray-500`} />;
    }
  };

  const getIconBg = (type: Notification["type"]) => {
    switch (type) {
      case "expense_submitted":
        return "bg-blue-100";
      case "approval":
        return "bg-green-100";
      case "rejection":
        return "bg-red-100";
      case "budget_warning":
        return "bg-orange-100";
      case "dispute_submitted":
        return "bg-orange-100";
      case "dispute_resolved":
        return "bg-green-100";
      default:
        return "bg-gray-100";
    }
  };

  const filterNotifications = (
    notifications: Notification[],
    filter: string
  ): Notification[] => {
    if (filter === "All") return notifications;

    const filterMap: Record<string, Notification["type"][]> = {
      Approvals: ["approval"],
      Escalations: ["budget_warning", "dispute_submitted"],
      Rejections: ["rejection"],
    };

    return notifications.filter((notification) =>
      filterMap[filter]?.includes(notification.type)
    );
  };

  const handleFilterChange = (filter: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setActiveFilter(filter);
      setIsLoading(false);
    }, 300);
  };

  const filteredNotifications = filterNotifications(
    notifications,
    activeFilter
  );
  const unreadCount = filteredNotifications.filter((n) => !n.isRead).length;

  const filters = ["All", "Approvals", "Escalations", "Rejections"];

  const formatDescription = (description: string) => {
    return description.split('"').map((part, index) => {
      if (index % 2 === 1) {
        return (
          <span key={index} className="font-medium text-gray-800">
            "{part}"
          </span>
        );
      }
      return part;
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
            Expense Management
          </h1>
          <p className="text-gray-600 text-sm sm:text-base">
            Manage your expenses, approvals, and notifications
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap gap-2 mb-6 sm:mb-8">
          {filters.map((filter) => (
            <button
              key={filter}
              onClick={() => handleFilterChange(filter)}
              className={`px-4 cursor-pointer py-2 sm:px-6 sm:py-3 rounded-lg font-medium transition-colors duration-300 whitespace-nowrap ${
                activeFilter === filter
                  ? "bg-cyan-400 text-white shadow-lg shadow-cyan-200"
                  : "bg-white text-gray-600 hover:bg-gray-100 shadow-sm"
              }`}
            >
              {filter}
              {filter === activeFilter && unreadCount > 0 && (
                <span className="ml-2 px-2 py-1 bg-white bg-opacity-30 rounded-full text-xs animate-pulse">
                  {unreadCount}
                </span>
              )}
            </button>
          ))}
        </div>

        {/* Content Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
          <div className="flex items-center gap-3">
            <h2 className="text-xl sm:text-2xl font-semibold text-gray-800">
              {activeFilter}
            </h2>
            {unreadCount > 0 && (
              <span className="px-3 py-1 bg-red-100 text-red-600 rounded-full text-sm font-medium animate-bounce">
                {unreadCount} new
              </span>
            )}
          </div>
          <button className="text-gray-500 hover:text-gray-700 text-sm sm:text-base transition-colors duration-200 self-start sm:self-auto">
            Mark all as read
          </button>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400"></div>
          </div>
        )}

        {/* Notifications List */}
        {!isLoading && (
          <div className="space-y-3 sm:space-y-4">
            {filteredNotifications.map((notification, index) => (
              <div
                key={notification.id}
                className={`flex items-start space-x-3 sm:space-x-4 p-4 sm:p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer transform hover:scale-[1.02] ${
                  !notification.isRead ? "border-l-4 border-cyan-400" : ""
                }`}
                style={{
                  animationDelay: `${index * 0.1}s`,
                  animation: "slideInUp 0.6s ease-out forwards",
                }}
              >
                {/* Icon */}
                <div
                  className={`p-2 sm:p-3 rounded-lg ${getIconBg(
                    notification.type
                  )} transition-transform duration-200 hover:scale-110`}
                >
                  {getIcon(notification.type)}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 gap-1">
                    <h3 className="font-semibold text-gray-800 text-sm sm:text-base">
                      {notification.title}
                    </h3>
                    {notification.time && (
                      <span className="text-xs sm:text-sm text-gray-500 flex-shrink-0">
                        {notification.time}
                      </span>
                    )}
                  </div>
                  <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                    {formatDescription(notification.description)}
                    {notification.type === "dispute_resolved" && (
                      <span className="text-cyan-500 font-medium ml-1 hover:underline">
                        see details.
                      </span>
                    )}
                  </p>
                </div>

                {/* Unread Indicator */}
                {!notification.isRead && (
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse flex-shrink-0 mt-2"></div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredNotifications.length === 0 && (
          <div className="text-center py-12 sm:py-16 animate-fadeIn">
            <div className="text-gray-400 mb-4">
              <FaInbox className="w-16 h-16 sm:w-20 sm:h-20 mx-auto" />
            </div>
            <h3 className="text-lg sm:text-xl font-medium text-gray-500 mb-2">
              No notifications
            </h3>
            <p className="text-gray-400 text-sm sm:text-base">
              No {activeFilter.toLowerCase()} notifications at the moment.
            </p>
          </div>
        )}
      </div>

      {/* <style jsx>{`
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }
      `}</style> */}
    </div>
  );
};

export default Notifications;
