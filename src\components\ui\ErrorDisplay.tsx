import React from "react";
import { MdErrorOutline } from "react-icons/md";

interface ErrorDisplayProps {
  error: unknown;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error }) => {
  const errorMessage =
    error instanceof Error ? error.message : "Something went wrong.";

  return (
    <div className="flex flex-col items-center justify-center p-6 rounded-lg bg-red-50 border border-red-200 shadow-md text-center max-w-md mx-auto mt-10">
      <MdErrorOutline className="text-red-500 text-5xl mb-3" />
      <h2 className="text-lg font-semibold text-red-700">Error</h2>
      <p className="text-sm text-red-600 mt-1">{errorMessage}</p>
    </div>
  );
};

export default ErrorDisplay;
