import React from "react";
import { FaCheckCircle, FaClock } from "react-icons/fa";
import { FcCancel } from "react-icons/fc";

interface QueryItem {
  id: string;
  description: string;
  submissionDate: string;
  status: "Pending" | "Answered" | "Canceled";
}

// Mock data for My Queries
const mockQueries: QueryItem[] = [
  {
    id: "Query#458",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Pending",
  },
  {
    id: "Query#459",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Answered",
  },
  {
    id: "Query#460",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Answered",
  },
  {
    id: "Query#461",
    description: "Exploring the world through travel and transportation opens",
    submissionDate: "2024-07-20",
    status: "Canceled",
  },
  {
    id: "Query#462",
    description: "How to optimize performance in React applications",
    submissionDate: "2024-07-21",
    status: "Pending",
  },
  {
    id: "Query#463",
    description: "Best practices for state management in large applications",
    submissionDate: "2024-07-21",
    status: "Answered",
  },
  {
    id: "Query#464",
    description: "Database optimization techniques for better performance",
    submissionDate: "2024-07-22",
    status: "Answered",
  },
  {
    id: "Query#465",
    description: "Security considerations for web applications",
    submissionDate: "2024-07-22",
    status: "Canceled",
  },
];

const MyQueries: React.FC = () => {
  //   const getStatusIcon = (status: string) => {
  //     switch (status) {
  //       case "Pending":
  //         return <Clock className="w-3 h-3 mr-1" />;
  //       case "Answered":
  //         return <CheckCircle className="w-3 h-3 mr-1" />;
  //       case "Canceled":
  //         return <XCircle className="w-3 h-3 mr-1" />;
  //       default:
  //         return null;
  //     }
  //   };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-yellow-100 text-yellow-800";
      case "Answered":
        return "bg-green-100 text-green-800";
      case "Canceled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="mb-8 nimate-slideUp" style={{ animationDelay: "0.2s" }}>
      <h2 className="text-2xl font-bold text-[#5D7285] mb-6">My Queries</h2>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Table Header */}
        <div className="bg-gray-50 px-4 md:px-6 py-4 border-b border-gray-200">
          <div className="grid grid-cols-4 gap-4 text-center text-sm font-medium text-gray-700">
            <div>Query ID</div>
            <div>Query Description</div>
            <div>Submission Date</div>
            <div>Query Status</div>
          </div>
        </div>

        {/* Table Body with Scroll */}
        <div className="max-h-[320px] overflow-y-auto divide-y divide-gray-200 scrollbar-hide">
          {mockQueries.map((query, index) => (
            <div
              key={`${query.id}-${index}`}
              className="px-4 md:px-6 py-4 hover:bg-gray-50 transition-colors text-center duration-200 animate-slideUp"
              style={{ animationDelay: "0.2s" }}
            >
              <div className="grid grid-cols-4 gap-4 items-center">
                <div className="text-sm text-gray-900 font-medium">
                  {query.id}
                </div>
                <div className="text-sm text-[slategray] text-left">
                  {query.description}
                </div>
                <div className="text-sm text-[slategray]">
                  {query.submissionDate}
                </div>
                <div className="text-sm">
                  <span
                    className={`inline-flex items-center px-5 py-2 rounded-full text-xs font-medium ${getStatusColor(
                      query.status
                    )}`}
                  >
                    {query.status === "Pending" && (
                      <FaClock className="w-3 h-3 mr-1" />
                    )}
                    {query.status === "Answered" && (
                      <FaCheckCircle className="w-3 h-3 mr-1" />
                    )}
                    {query.status === "Canceled" && (
                      <FcCancel className="w-3 h-3 mr-1" />
                    )}
                    {query.status}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MyQueries;
