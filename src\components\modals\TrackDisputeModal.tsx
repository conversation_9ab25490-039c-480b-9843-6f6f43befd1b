import React, { useState } from "react";
import { IoImageOutline } from "react-icons/io5";
import ModalImage from "react-modal-image";
import type { DisputeData } from "../../types/expenseTypes";

interface TrackDisputeModalProps {
  isTrackDisputeModalOpen: boolean;
  handleCloseTrackDisputeModal: () => void;
  selectedExpense: DisputeData;
}

const TrackDisputeModal: React.FC<TrackDisputeModalProps> = ({
  isTrackDisputeModalOpen,
  handleCloseTrackDisputeModal,
  selectedExpense,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "In Review":
        return "bg-[darkturquoise] text-white";
      case "Approved":
        return "bg-[seagreen] text-white";
      case "Rejected":
        return "bg-[tomato] text-white";
      case "Pending":
        return "bg-[goldenrod] text-white";
      default:
        return "bg-[darkgray] text-white";
    }
  };

  if (!isTrackDisputeModalOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center p-4 z-50 animate-fadeIn duration-300">
      <div className="bg-white rounded-2xl w-full max-w-4xl mx-auto shadow-2xl transform animate-slideIn transition-all duration-300 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-[darkslateblue]">
            Track Dispute
          </h2>
          <button
            onClick={handleCloseTrackDisputeModal}
            className="text-red-500 cursor-pointer hover:text-red-700 font-medium text-sm transition-colors duration-200 hover:scale-105"
          >
            Close
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Dispute Header Info */}
          <div className="bg-white shadow-2xl rounded-xl p-4 space-y-3 animate-slideUp">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div>
                <h3 className="font-semibold text-[slategray] text-lg">
                  {selectedExpense.title}
                </h3>
                <p className="text-sm text-[darkgray]">
                  {selectedExpense.date}
                </p>
              </div>
              <div className="flex items-center gap-3">
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                    selectedExpense.status
                  )} `}
                >
                  {selectedExpense.status}
                </span>
              </div>
            </div>

            {/* Amount and Category */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-sm">₹</span>
                </div>
                <span className="font-semibold text-[darkslateblue]">
                  {Number(selectedExpense.amount).toFixed(2) ||
                    selectedExpense.amount}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-xs">🍽️</span>
                </div>
                <span className="text-[darkslateblue] text-sm">
                  {selectedExpense.category}
                </span>
              </div>
            </div>

            {/* Description */}
            <div className="pt-2">
              <p className="text-gray-600 text-sm leading-relaxed">
                {selectedExpense.description}
              </p>
            </div>
          </div>

          {/* Dispute Reason Section */}
          <div
            className="space-y-3 animate-slideUp bg-white rounded-2xl p-5 shadow-2xl"
            style={{ animationDelay: "0.1s" }}
          >
            <h4 className="text-lg font-semibold text-[darkslateblue]">
              Dispute Reason
            </h4>
            <div className=" rounded-lg text-justify">
              <p className="text-gray-700 text-sm leading-relaxed">
                {selectedExpense.disputeReason}
              </p>
            </div>
          </div>

          {/* Attached Evidence Section */}
          <div
            className="space-y-3 animate-slideUp"
            style={{ animationDelay: "0.2s" }}
          >
            <h4 className="text-lg font-semibold text-[darkslateblue]">
              Attached Evidence
            </h4>
            <div className="flex flex-wrap flex-row sm:flex-1 sm:justify-center sm:items-center gap-5">
              {(selectedExpense.attachedEvidence ?? []).map(
                (evidence, index) => (
                  <PreviewImage
                    key={index}
                    src={evidence.url}
                    alt={evidence.name ?? "Attachment"}
                    idx={index}
                  />
                )
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackDisputeModal;

interface AttachmentProps {
  src: string;
  alt: string;
  idx: number;
}

const PreviewImage: React.FC<AttachmentProps> = ({ src, alt, idx }) => {
  const [error, setError] = useState(false);

  return (
    <div
      key={idx}
      className="w-[80px] h-[100px]  overflow-hidden  bg-white shadow-2xl  border-2 border-gray-300 rounded-lg"
    >
      {error ? (
        <div className="w-full h-full bg-gray-100  flex items-center justify-center text-gray-400 border border-gray-200 rounded">
          <IoImageOutline className="w-6 h-6" />
        </div>
      ) : (
        <ModalImage
          small={src}
          large={src}
          alt={alt}
          hideDownload={true}
          hideZoom={true}
          className="object-cover w-full h-full cursor-pointer"
          onError={() => setError(true)}
        />
      )}
    </div>
  );
};
