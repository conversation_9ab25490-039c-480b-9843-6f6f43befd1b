import React, { useState } from "react";
import { FaChevronLeft } from "react-icons/fa";
import { useRouter } from "@tanstack/react-router"; // Changed from react-router-dom
import QueryForm from "../../components/support/QueryForm";
import MyQueries from "../../components/support/MyQueries";
import FAQ from "../../components/support/faqData";
import SupportModal from "../../components/modals/SupportModal";

interface SupportProps {
  onBack?: () => void;
}

const Support: React.FC<SupportProps> = ({ onBack }) => {
  const [modal, setModal] = useState<{
    isOpen: boolean;
    type: "success" | "error";
    title: string;
    message: string;
    icons: string | null;
  }>({
    isOpen: false,
    type: "success",
    title: "",
    message: "",
    icons: "",
  });

  const router = useRouter(); // Changed from useNavigate

  const successQueryIcon =
    "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%2310B981'/%3E%3Cpath d='M35 50 L45 60 L65 40' stroke='white' stroke-width='4' fill='none' stroke-linecap='round'/%3E%3C/svg%3E";

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.navigate({ to: "/profile" }); // Updated navigation
    }
  };

  const handleQuerySubmit = async (query: string, image: File | null) => {
    console.log(query, "query", image, "image");
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      const isSuccess = Math.random() > 0.2;

      if (isSuccess) {
        setModal({
          isOpen: true,
          type: "success",
          title: "Query Submitted Successfully",
          icons: successQueryIcon,
          message: "You have successfully submitted your query...",
        });
      } else {
        setModal({
          isOpen: true,
          type: "error",
          title: "Submission Failed",
          icons: null,
          message: "We encountered an error while submitting...",
        });
      }
    } catch (error) {
      console.error("Submission error:", error);
      setModal({
        isOpen: true,
        type: "error",
        title: "Network Error",
        message: "Please check your internet connection...",
        icons: null,
      });
      throw error;
    }
  };

  const closeModal = () => {
    setModal((prev) => ({ ...prev, isOpen: false }));
  };

  return (
    <div className="min-h-screen bg-grey-100 md:p-6 lg:p-8">
      <div className="max-w-5xl mx-auto bg-white shadow-lg rounded-lg p-5">
        {/* Header */}
        <div className="mb-8 animate-fadeIn">
          <button
            onClick={handleBack}
            className="flex items-center cursor-pointer text-teal-600 hover:text-teal-700 mb-4 transition-transform duration-300 hover:scale-110"
            aria-label="Go back"
          >
            <FaChevronLeft className="w-5 h-5 mr-1 transition-transform duration-300 hover:scale-110" />
            Back
          </button>
          <h1 className="text-3xl font-bold mb-2">Support</h1>
        </div>

        <QueryForm onSubmit={handleQuerySubmit} />
        <div className="animate-slideUp" style={{ animationDelay: "0.1s" }}>
          <MyQueries />
        </div>
        <div className="animate-slideUp" style={{ animationDelay: "0.2s" }}>
          <FAQ />
        </div>
        <SupportModal {...modal} onClose={closeModal} />
      </div>
    </div>
  );
};

export default Support;
