import { useParams } from '@tanstack/react-router';

const EmployeeSummaryCard = () => {
  const { id } = useParams({ strict: false });
  
  const getUserData = (userId: string) => {
    const users = {
      '1': { name: '<PERSON>', department: 'BI', employeeId: 'EMP001' },
      '2': { name: '<PERSON>', department: 'Development', employeeId: 'EMP002' },
      '3': { name: '<PERSON>', department: 'Design', employeeId: 'EMP003' },
      '4': { name: '<PERSON>', department: 'Marketing', employeeId: 'EMP004' },
      '5': { name: '<PERSON>', department: 'Marketing', employeeId: 'EMP005' },
      '6': { name: '<PERSON>', department: 'Marketing', employeeId: 'EMP006' },
      '7': { name: '<PERSON>', department: 'BI', employeeId: 'EMP007' },
    };
    return users[userId as keyof typeof users] || { name: 'Unknown User', department: 'Unknown', employeeId: 'N/A' };
  };

  const userData = getUserData(id || '1');

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-8">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{userData.name}</h1>
          <p className="text-gray-500 font-semibold mb-1">{userData.department} Department</p>
          <p className="text-sm text-gray-500">
            Employee ID: {userData.employeeId} | Latest Submission Date: 2024-07-20
          </p>
        </div>

        <div className="flex-[1.5] w-full sm:w-auto sm:max-w-xl mt-4 sm:mt-0">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-500 h-2 rounded-full" style={{ width: '70%' }}></div>
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-gray-500 mt-2">Usage Limit</span>
            <p className="text-md font-semibold text-gray-600 mt-2">$350.60 / $50000</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-500 mb-2">Total Amount</h3>
          <p className="text-2xl font-bold text-gray-600">$12,500.00</p>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-500 mb-2">Current Status</h3>
          <p className="text-2xl font-bold text-gray-600">Approved</p>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-500 mb-2">Average Spending</h3>
          <p className="text-2xl font-bold text-gray-600">$230.00</p>
          <p className="text-sm text-orange-500 mt-1">-5%</p>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-sm font-semibold text-gray-500 mb-2">Policy Compliance Summary</h3>
          <p className="text-2xl font-bold text-gray-600">Compliant</p>
          <p className="text-sm text-gray-400 mt-1">as of recent activities</p>
        </div>
      </div>
    </div>
  );
};

export default EmployeeSummaryCard;
