type StatusFilter = "All" | "Pending" | "Approved" | "Rejected";
type TimeFilter = "12 months" | "30 days" | "7 days" | "24 hours";
type Filter = StatusFilter | TimeFilter;

type ButtonGroupProps<T extends Filter> = {
  options: readonly T[];
  selected: T;
  onChange: (value: T) => void;
  orientation?: "horizontal" | "vertical";
  className?: string; // applies to the container
  buttonClassName?: string; // optional: apply to each button
};

const statusColorMap: Record<StatusFilter, string> = {
  All: "darkAccent text-white",
  Pending: "bg-[darkorange] text-white",
  Approved: "bg-[mediumseagreen] text-white",
  Rejected: "bg-red-600 text-white",
};

const timeColorMap: Record<TimeFilter, string> = {
  "12 months": "darkAccent text-white",
  "30 days": "darkAccent text-white",
  "7 days": "darkAccent text-white",
  "24 hours": "darkAccent text-white",
};

const getColorForOption = (option: Filter): string => {
  if (option in statusColorMap) {
    return statusColorMap[option as StatusFilter];
  }
  if (option in timeColorMap) {
    return timeColorMap[option as TimeFilter];
  }
  return "bg-gray-600 text-white";
};

const ButtonGroup = <T extends Filter>({
  options,
  selected,
  onChange,
  orientation = "horizontal",
  className = "",
  buttonClassName = "",
}: ButtonGroupProps<T>) => {
  const isVertical = orientation === "vertical";
  const containerClass = isVertical
    ? "inline-flex flex-col rounded-md shadow-sm"
    : "inline-flex flex-row rounded-md shadow-sm";

  return (
    <div className={`${containerClass} ${className}`} role="group">
      {options.map((option, index) => {
        const isFirst = index === 0;
        const isLast = index === options.length - 1;

        const rounded = isVertical
          ? isFirst
            ? "rounded-t-md"
            : isLast
            ? "rounded-b-md"
            : ""
          : isFirst
          ? "rounded-l-md"
          : isLast
          ? "rounded-r-md"
          : "";

        const isActive = selected === option;
        const activeClass = isActive
          ? getColorForOption(option)
          : "bg-white text-gray-700 hover:bg-gray-100 border border-gray-300";

        return (
          <button
            key={option}
            type="button"
            onClick={() => onChange(option)}
            className={`
              px-3 py-2 text-xs sm:text-sm font-medium focus:outline-none cursor-pointer
              ${rounded}
              ${activeClass}
              ${buttonClassName}
              ${!isVertical && index !== 0 ? "-ml-px" : ""}
              ${isVertical && index !== 0 ? "-mt-px" : ""}
              transition-colors duration-200
              whitespace-nowrap
              min-w-0
              flex-shrink-0
            `}
          >
            <span className="truncate">{option}</span>
          </button>
        );
      })}
    </div>
  );
};

export default ButtonGroup;