@import "tailwindcss";

@theme {
  --font-sans: "Plus Jakarta Sans", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

.brand-gradient {
  background: linear-gradient(90deg, #2b5162 0%, #12242c 100%);
}

.latest-reimbursement-card-gradient {
  background: linear-gradient(180deg, #0ed5d5 0%, #00ffaa 100%);
}

.dark-accent {
  background: linear-gradient(270deg,
      #0f2027 -9.2%,
      #203a43 47.44%,
      #2c5364 104.07%);
}

.darkAccent {
  background: linear-gradient(270deg,
      #0f2027 -9.2%,
      #203a43 47.44%,
      #2c5364 104.07%);
  color: white;
}

.darkgray {
  color: #a4a4a4;
}

.slategray {
  color: #5d7285;
}

.darkturquoise {
  color: #00c5c5;
}

.turquoise {
  color: #00c8b1;
}

.aliceblue {
  color: #f4f7fd;
}

.whitesmoke {
  color: #f0f8f8;
}

.dimgray {
  color: #6e6e6e;
}

.seagreen {
  color: #10813a;
}

.steelblue {
  color: #4963a1;
}

.darkcyan {
  color: #009a9a;
}

.goldenrod {
  color: #d89017;
}

.gainsboro {
  color: #e4e4e9;
}

.azure {
  color: #f5fcfc;
}

.darkslateblue {
  color: #20315a;
}

.tomato {
  color: #eb4335;
}

.forestgreen {
  color: #088738;
}

.silver {
  color: #c7c7c7;
}

.whitesmoke {
  color: #f6f6f6;
}

.darkorange {
  color: #ff9811;
}

.limegreen {
  color: #06b217;
}

.mediumseagreen {
  color: #00be5c;
}

.lavender {
  color: #CEDFEF;
}

.Darkturquoise {
  color: #00C5C5;
}
.Slategray{
  color: #5C738A;
  font-weight: 400;
  font-size: 24px;
}
.shadow-custom-desc{
  box-shadow: 0px 1px 6px 0px #0A0D1217;

}
.custom-reject-btn{
  background-color: #EB4335;
  width: 277px;
  padding: 10px 0px 10px 0px;
  border-radius: 5px;
  color: white;
  font-size: 16px;
  font-weight: 500;
}
.custom-approve-btn{
  background-color: #C7C7C7;
  width: 277px;
  padding: 10px 0px 10px 0px;
  border-radius: 5px;
  color: white;
  font-size: 16px;
  font-weight: 500;
}


@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out;
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

/* scroll bar */
::-webkit-scrollbar {
  width: 0px;
}

::-webkit-scrollbar-track {
  background: #e0f7f7;
}

::-webkit-scrollbar-thumb {
  background-color: #008080;
  border-radius: 6px;
  border: 2px solid #e0f7f7;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #006666;
}