import React, { useState } from "react";
import ExpenseTrackerModal from "../modals/ExpenseTrackerModal";

import { FaCheckCircle, FaTimesCircle } from "react-icons/fa";
import { FaQuestion } from "react-icons/fa6";
import type {
  ApprovalStep,
  Expense,
  ExpenseItem,
} from "../../types/expenseTypes";
import { expensesData } from "../../db/expenseData";
import ButtonGroup from "../ButtonGroup";


const FILTERS = ["All", "Pending", "Approved", "Rejected"] as const;

interface SelectedExpenseReport {
  reportId: number;
  reportTitle: string;
  reportAmount: string;
  reportStatus: "Pending" | "Approved" | "Rejected";
  reportApprovals: ApprovalStep[];
  expenses: ExpenseItem[];
}

const ExpenseTable: React.FC = () => {
  const [selectedFilter, setSelectedFilter] =
    useState<(typeof FILTERS)[number]>("All");
  const [isOpenTrackDisputeModal, setIsOpenTrackDisputeModal] =
    useState<boolean>(false);
  const [selectedExpenseReport, setSelectedExpenseReport] =
    useState<SelectedExpenseReport | null>(null);

  const handleViewClick = (expense: Expense) => {
    // Map the expense data to the format expected by the modal
    const mappedExpenseReport: SelectedExpenseReport = {
      reportId: expense.id,
      reportTitle: expense.title,
      reportAmount: expense.amount,
      reportStatus: expense.status,
      reportApprovals: expense.approvals,
      expenses: expense.expenses || [], // Use the expenses array or empty array
    };

    setSelectedExpenseReport(mappedExpenseReport);
    setIsOpenTrackDisputeModal(true);
  };

  const handleCloseTackerDispute = () => {
    setIsOpenTrackDisputeModal(false);
    setSelectedExpenseReport(null);
  };

  const filteredExpenses =
    selectedFilter === "All"
      ? expensesData
      : expensesData.filter((e) => e.status === selectedFilter);

  return (
    <>
      <div className="p-6 bg-white rounded-lg w-full">
        {/* Header and Filters */}
        <div className="mb-6 flex flex-wrap items-center justify-between gap-3">
          <h2 className="text-sky-900 font-bold text-md">All Activity</h2>

          <div className="flex items-center gap-3">
            <input
              type="text"
              className="border border-gray-300 rounded-sm py-1.5 px-2 text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2"
              aria-label="Filter by date"
              placeholder="Date"
            />

            <ButtonGroup
              options={FILTERS}
              selected={selectedFilter}
              onChange={setSelectedFilter}
              className="cursor-pointer rounded-2xl"
            />
          </div>
        </div>

        {/* Table Container with Fixed Height and Scroll */}
        <div className="w-full overflow-hidden  ">
          <div className="max-h-[400px] overflow-y-auto scrollbar-hide">
            <table className="w-full text-left border-collapse text-md">
              <thead className="sticky top-0 bg-white z-10">
                <tr className="border-b border-gray-200 text-gray-700 font-medium text-sm">
                  <th className="py-3 px-4 min-w-[150px]">Expense Report</th>
                  <th className="py-3 px-4 min-w-[120px]">Description</th>
                  <th className="py-3 px-4 min-w-[100px]">Category</th>
                  <th className="py-3 px-4 min-w-[120px]">Submission Date</th>
                  <th className="py-3 px-4 min-w-[80px]">Amount</th>
                  <th className="py-3 px-4 min-w-[100px]">Usage Limit</th>
                  <th className="py-3 px-4 min-w-[120px]">Approvals</th>
                  <th className="py-3 px-4 min-w-[80px]">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredExpenses.length === 0 && (
                  <tr>
                    <td colSpan={8} className="text-center py-10 text-gray-500">
                      No expenses found.
                    </td>
                  </tr>
                )}

                {filteredExpenses.map((expense) => (
                  <tr
                    key={expense.id}
                    className="border-b border-gray-100 hover:bg-gray-50 transition-colors text-lg"
                  >
                    <td className="py-4 text-sm px-4 align-top text-gray-800">
                      <div className="flex flex-col">
                        <span className="break-words">{expense.title}</span>
                        {expense.expenses && expense.expenses.length > 1 && (
                          <span className="text-xs text-gray-500 mt-1">
                            ({expense.expenses.length} expenses)
                          </span>
                        )}
                      </div>
                    </td>

                    <td
                      className="text-sm py-4 px-4 align-top font-semibold text-gray-600"
                      title={expense.description}
                    >
                      <div className="break-words">
                        {expense.description.length > 35
                          ? expense.description.substring(0, 35) + "…"
                          : expense.description}
                      </div>
                    </td>

                    <td className="text-sm py-4 font-semibold px-4 align-top text-gray-500">
                      <div className="flex items-center gap-2">
                        <img
                          src="categoryIcon.svg"
                          alt=""
                          className="text-gray-600 flex-shrink-0"
                        />
                        <span className="break-words">{expense.category}</span>
                      </div>
                    </td>

                    <td className="text-sm py-4 px-4 align-top font-semibold text-gray-500 cursor-pointer">
                      <span className="break-words">
                        {expense.submissionDate}
                      </span>
                    </td>

                    <td className="text-sm py-4 px-4 align-top text-gray-500 font-bold">
                      <span className="break-words">{expense.amount}</span>
                    </td>

                    <td className="text-sm py-4 px-4 font-semibold align-top text-gray-500">
                      <span className="break-words">{expense.usageLimit}</span>
                    </td>

                    <td className="py-2 px-1 align-top">
                      <div className="flex gap-1 bg-gray-100 rounded-2xl items-center justify-center p-2">
                        {expense.approvals.map((approval, idx) => {
                          const getIcon = () => {
                            switch (approval.status) {
                              case "Approved":
                                return (
                                  <FaCheckCircle
                                    className="text-teal-500 bg-green-100 rounded-full flex-shrink-0"
                                    size={20}
                                    title="Approved"
                                  />
                                );
                              case "Rejected":
                                return (
                                  <FaTimesCircle
                                    className="text-red-500 bg-red-100 rounded-full flex-shrink-0"
                                    size={20}
                                    title="Rejected"
                                  />
                                );
                              case "Pending":
                              default:
                                return (
                                  <FaQuestion
                                    className="text-gray-500 bg-gray-100 rounded-full flex-shrink-0"
                                    size={20}
                                    title="Pending"
                                  />
                                );
                            }
                          };

                          return (
                            <React.Fragment key={idx}>
                              {getIcon()}
                              {idx < expense.approvals.length - 1 && (
                                <div className="w-[1px] min-h-6 bg-gradient-to-b from-transparent via-cyan-900 to-transparent mx-1" />
                              )}
                            </React.Fragment>
                          );
                        })}
                      </div>
                    </td>

                    <td className="py-4 px-4 align-top text-sm text-gray-500 font-bold cursor-pointer select-none">
                      <button
                        className="cursor-pointer hover:underline"
                        onClick={() => handleViewClick(expense)}
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {isOpenTrackDisputeModal && selectedExpenseReport && (
        <ExpenseTrackerModal
          handleCloseTackerDispute={handleCloseTackerDispute}
          isOpenTrackDisputeModal={isOpenTrackDisputeModal}
          selectedExpenseReport={selectedExpenseReport}
        />
      )}
    </>
  );
};

export default ExpenseTable;
