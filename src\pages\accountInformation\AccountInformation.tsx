import React, { useState, useEffect } from "react";
import { useRouter } from "@tanstack/react-router";
import { FaChevronLeft } from "react-icons/fa";
import { LuBuilding2 } from "react-icons/lu";

interface AccountInformationProps {
  onBack?: () => void;
}

const AccountInformation: React.FC<AccountInformationProps> = ({ onBack }) => {
  const router = useRouter(); 
  const [isVisible, setIsVisible] = useState(false);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.history.back(); // Updated navigation
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const userInfo = {
    name: "<PERSON>",
    avatar: "/api/placeholder/40/40",
    contactNumber: "***********",
    email: "<EMAIL>",
    company: {
      name: "Tech Solutions Inc.",
      role: "Developer",
      department: "Software Development",
      reportingTo: "Manager Name",
    },
  };

  const InfoRow = ({
    label,
    value,
    className = "",
    delay = 0,
  }: {
    label: string;
    value: string;
    className?: string;
    delay?: number;
  }) => (
    <div
      className={`flex flex-col sm:flex-row sm:justify-between sm:items-center py-3 px-2 sm:px-0 transform transition-all duration-700 ease-out hover:bg-gray-50 hover:px-2 hover:mx-[-8px] hover:rounded-md ${className} ${
        isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
      }`}
      style={{ transitionDelay: `${delay}ms` }}
    >
      <span className="text-gray-600 text-sm sm:text-base transition-colors duration-200 mb-1 sm:mb-0">
        {label}:
      </span>
      <span className="text-gray-900 font-medium text-sm sm:text-base transition-colors duration-200 break-all sm:break-normal">
        {value}
      </span>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <button
          onClick={handleBack}
          className={`flex items-center gap-2 cursor-pointer text-teal-600 hover:text-teal-700 mb-6 transition-all duration-300 ease-out hover:gap-3 transform ${
            isVisible ? "translate-x-0 opacity-100" : "-translate-x-4 opacity-0"
          }`}
          aria-label="Go back"
        >
          <FaChevronLeft
            size={20}
            className="transition-transform duration-300 hover:scale-110"
          />
          <span className="font-medium text-sm sm:text-base">Back</span>
        </button>

        {/* Main Content */}
        <div
          className={`bg-white rounded-lg shadow-sm p-4 sm:p-6 transition-all duration-800 ease-out transform ${
            isVisible
              ? "translate-y-0 opacity-100 scale-100"
              : "translate-y-8 opacity-0 scale-95"
          }`}
        >
          {/* Header */}
          <h1
            className={`text-xl sm:text-2xl font-semibold text-gray-900 mb-6 sm:mb-8 transition-all duration-700 ease-out transform ${
              isVisible
                ? "translate-y-0 opacity-100"
                : "translate-y-4 opacity-0"
            }`}
            style={{ transitionDelay: "200ms" }}
          >
            Account Information
          </h1>

          {/* User Profile Section */}
          <div
            className={`flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8 transition-all duration-700 ease-out transform ${
              isVisible
                ? "translate-y-0 opacity-100"
                : "translate-y-4 opacity-0"
            }`}
            style={{ transitionDelay: "300ms" }}
          >
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-blue-500/25 flex-shrink-0">
              <span className="text-white font-semibold text-sm sm:text-lg transition-all duration-300">
                {userInfo.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </span>
            </div>
            <div className="min-w-0 flex-1">
              <h2 className="text-base sm:text-lg font-semibold text-gray-900 transition-colors duration-200 hover:text-teal-600 truncate">
                {userInfo.name}
              </h2>
            </div>
          </div>

          {/* Personal Information Section */}
          <div
            className={`mb-6 sm:mb-8 transition-all duration-700 ease-out transform ${
              isVisible
                ? "translate-y-0 opacity-100"
                : "translate-y-4 opacity-0"
            }`}
            style={{ transitionDelay: "400ms" }}
          >
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 transition-colors duration-200 hover:text-teal-600">
              Personal information
            </h3>
            <div className="space-y-1 border-b border-gray-100 pb-4">
              <InfoRow
                label="Contact number"
                value={userInfo.contactNumber}
                delay={500}
              />
              <InfoRow label="Email ID" value={userInfo.email} delay={600} />
            </div>
          </div>

          {/* Company Information Section */}
          <div
            className={`transition-all duration-700 ease-out transform ${
              isVisible
                ? "translate-y-0 opacity-100"
                : "translate-y-4 opacity-0"
            }`}
            style={{ transitionDelay: "500ms" }}
          >
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 transition-colors duration-200 hover:text-teal-600">
              Company Information
            </h3>

            {/* Company Badge */}
            <div
              className={`flex items-center gap-3 mb-6 transition-all duration-700 ease-out transform ${
                isVisible
                  ? "translate-y-0 opacity-100"
                  : "translate-y-4 opacity-0"
              }`}
              style={{ transitionDelay: "600ms" }}
            >
              <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center transition-all duration-300 hover:scale-110 hover:bg-teal-700 hover:shadow-lg flex-shrink-0">
                <LuBuilding2
                  size={16}
                  className="text-white transition-all duration-300"
                />
              </div>
              <span className="font-medium text-gray-900 transition-colors duration-200 hover:text-teal-600 text-sm sm:text-base truncate">
                {userInfo.company.name}
              </span>
            </div>

            {/* Company Details */}
            <div className="space-y-1">
              <InfoRow label="Role" value={userInfo.company.role} delay={700} />
              <InfoRow
                label="Department"
                value={userInfo.company.department}
                delay={800}
              />
              <InfoRow
                label="Reporting to"
                value={userInfo.company.reportingTo}
                delay={900}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountInformation;
