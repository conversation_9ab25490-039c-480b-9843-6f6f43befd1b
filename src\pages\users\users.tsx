import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import Dropdown from "../../components/Dropdown";
import DatePicker from "../../components/DatePicker";
import Pagination from "../../components/Pagination";

interface User {
  id: string;
  name: string;
  email: string;
  department: string;
  role: string;
  status: "Active" | "Inactive";
  lastLogin: string;
  avatar: string;
}

const ITEMS_PER_PAGE = 6;

const mockUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "BI",
    role: "Manager",
    status: "Active",
    lastLogin: "2 days ago",
    avatar: "AB"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Development",
    role: "Finance",
    status: "Active",
    lastLogin: "2 days ago",
    avatar: "J<PERSON>"
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Design",
    role: "Employee",
    status: "Active",
    lastLogin: "2 days ago",
    avatar: "J<PERSON>"
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Executive",
    status: "Inactive",
    lastLogin: "2 weeks ago",
    avatar: "ED"
  },
  {
    id: "5",
    name: "Michael <PERSON>",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Executive",
    status: "Inactive",
    lastLogin: "2 weeks ago",
    avatar: "MJ"
  },
  {
    id: "6",
    name: "Sarah Williams",
    email: "<EMAIL>",
    department: "Marketing",
    role: "Manager",
    status: "Active",
    lastLogin: "2 weeks ago",
    avatar: "SW"
  },
  {
    id: "7",
    name: "David Anderson",
    email: "<EMAIL>",
    department: "BI",
    role: "Manager",
    status: "Inactive",
    lastLogin: "3 days ago",
    avatar: "DA"
  }
];

export default function Users() {
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedRole, setSelectedRole] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const roleOptions = ["Manager", "Finance", "Employee", "Executive"];
  const departmentOptions = ["BI", "Development", "Design", "Marketing"];
  const statusOptions = ["Active", "Inactive"];

  const filteredUsers = mockUsers.filter(user => {
    return (
      (!selectedRole || user.role === selectedRole) &&
      (!selectedDepartment || user.department === selectedDepartment) &&
      (!selectedStatus || user.status === selectedStatus)
    );
  });

  const totalPages = Math.ceil(filteredUsers.length / ITEMS_PER_PAGE);
  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handleViewEdit = (userId: string) => {
    navigate({ to: `/users/${userId}` });
  };

  return (
    <div className="min-h-screen max-w-7xl mx-auto p-3 sm:p-4 lg:p-5">

      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between gap-4">
              <h1 className="text-2xl font-semibold text-gray-900">All Users</h1>
              <div className="flex flex-wrap justify-center items-center gap-4">
                <DatePicker
                  selected={selectedDate}
                  onChange={setSelectedDate}
                  placeholderText="Date"
                />
                <Dropdown
                  value={selectedRole}
                  onChange={setSelectedRole}
                  options={roleOptions}
                  label="Role"
                  placeholder="Role"
                />
                <Dropdown
                  value={selectedDepartment}
                  onChange={setSelectedDepartment}
                  options={departmentOptions}
                  label="Department"
                  placeholder="Department"
                />
                <Dropdown
                  value={selectedStatus}
                  onChange={setSelectedStatus}
                  options={statusOptions}
                  label="Status"
                  placeholder="Status"
                />
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full min-w-[640px] mt-4">
              <thead className="border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase">Name</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase hidden sm:table-cell">Email</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase">Department</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase hidden lg:table-cell">Last Login</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-700 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full flex items-center justify-center">
                          <img
                            src="https://avatar.iran.liara.run/public"
                            alt={user.avatar}
                          />

                          {/* when image is not available conditionally render it to show the default image placeholder */}
                          {/* <img
                            src="/avatar.svg"
                            alt={user.avatar}
                          /> */}
                        </div>
                        <span className="text-sm font-medium text-gray-900">{user.name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 hidden sm:table-cell">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{user.department}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className="bg-gray-100 px-2 py-1 rounded-full text-xs font-medium">{user.role}</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${user.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                        }`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell">
                      {user.lastLogin}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-semibold">
                      <button
                        className="hover:text-gray-800"
                        onClick={() => handleViewEdit(user.id)}
                      >
                        View/Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="p-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
