// All necessary imports
import React, { useState } from "react";
import {
  AlertTriangle,
  CheckCircle,
  User,
  Calendar,
  DollarSign,
  FileText,
} from "lucide-react";

// Types
interface ExpenseItem {
  id: string;
  category: string;
  title: string;
  amount: number;
  date: string;
  description: string;
  uploads: string[];
  markedForRejection: boolean;
}

interface ExpenseRequest {
  id: string;
  employee: string;
  expenseName: string;
  category: string;
  submissionDate: string;
  amount: number;
  isMultiple: boolean;
  expenses?: ExpenseItem[];
  isCompliant: boolean;
  reason?: string;
}

// Placeholder for ExpenseDetailsModal
const ExpenseDetailsModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  expense: ExpenseRequest | null;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
}> = ({ isOpen, onClose, expense, onApprove, onReject }) => {
  const [rejectionToggles, setRejectionToggles] = useState<{
    [key: string]: boolean;
  }>({});

  if (!isOpen || !expense) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-md max-w-lg w-full">
        <h2 className="text-xl font-bold mb-4">{expense.expenseName}</h2>
        <p className="mb-4">Submitted by: {expense.employee}</p>
        <p className="mb-4">Amount: ${expense.amount.toFixed(2)}</p>
        <div className="flex gap-3 justify-end">
          <button
            onClick={() => onReject(expense.id)}
            className="bg-red-100 text-red-700 px-4 py-2 rounded"
          >
            Reject
          </button>
          <button
            onClick={() => onApprove(expense.id)}
            className="bg-green-100 text-green-700 px-4 py-2 rounded"
          >
            Approve
          </button>
          <button
            onClick={onClose}
            className="bg-gray-200 text-gray-700 px-4 py-2 rounded"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

// Stats Card Component
const StatsCard: React.FC<{
  title: string;
  value: number;
  change: string;
  color: string;
}> = ({ title, value, change, color }) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
    <h3 className="text-gray-500 text-sm font-medium mb-2">{title}</h3>
    <div className="flex items-end justify-between">
      <span className="text-3xl font-bold text-gray-900">{value}</span>
      <span className={`text-sm font-medium ${color}`}>{change}</span>
    </div>
  </div>
);

// Category Badge Component
const CategoryBadge: React.FC<{ category: string; isMultiple?: boolean }> = ({
  category,
  isMultiple,
}) => {
  const getIcon = () => {
    if (isMultiple) return <FileText className="w-4 h-4" />;
    if (category.includes("Travel")) return <Calendar className="w-4 h-4" />;
    return <DollarSign className="w-4 h-4" />;
  };

  return (
    <span className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
      {getIcon()}
      {category}
    </span>
  );
};

// Mock data
const mockExpenseRequests: ExpenseRequest[] = [
  // mock data goes here (as in your code)
];

const statsData = [
  { title: "All Claims", value: 57, change: "+10%", color: "text-blue-600" },
  { title: "Approved Claims", value: 23, change: "+8%", color: "text-green-600" },
  { title: "Pending Approval", value: 4, change: "-5%", color: "text-orange-600" },
  { title: "Team Members", value: 7, change: "-", color: "text-purple-600" },
];

// Main Component
const FinanceApprovals: React.FC = () => {
  const [selectedExpense, setSelectedExpense] = useState<ExpenseRequest | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expenses, setExpenses] = useState<ExpenseRequest[]>(mockExpenseRequests);

  const handleViewExpense = (expense: ExpenseRequest) => {
    setSelectedExpense(expense);
    setIsModalOpen(true);
  };

  const handleApprove = (id: string) => {
    setExpenses((prev) => prev.filter((exp) => exp.id !== id));
    setIsModalOpen(false);
    setSelectedExpense(null);
  };

  const handleReject = (id: string) => {
    setExpenses((prev) => prev.filter((exp) => exp.id !== id));
    setIsModalOpen(false);
    setSelectedExpense(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <div
              key={stat.title}
              className="animate-slideUp"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <StatsCard {...stat} />
            </div>
          ))}
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 animate-slideUp" style={{ animationDelay: "400ms" }}>
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Expense requests Finance</h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="text-left py-4 px-6 font-medium text-gray-700">Employee</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700">Expense Name</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700">Category</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700">Submission Date</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700">Amount</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {expenses.map((expense, index) => (
                  <tr
                    key={expense.id}
                    className="hover:bg-gray-50 transition-colors animate-slideUp"
                    style={{ animationDelay: `${500 + index * 100}ms` }}
                  >
                    <td className="py-4 px-6">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600" />
                        </div>
                        <span className="font-medium text-gray-900">{expense.employee}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-gray-700">{expense.expenseName}</td>
                    <td className="py-4 px-6">
                      <CategoryBadge category={expense.category} isMultiple={expense.isMultiple} />
                    </td>
                    <td className="py-4 px-6 text-gray-600">{expense.submissionDate}</td>
                    <td className="py-4 px-6 font-semibold text-gray-900">${expense.amount.toFixed(2)}</td>
                    <td className="py-4 px-6">
                      <button
                        onClick={() => handleViewExpense(expense)}
                        className="bg-purple-100 hover:bg-purple-200 px-7 py-1 cursor-pointer text-sm text-purple-700 rounded-full font-medium transition-colors flex items-center gap-2"
                      >
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      <ExpenseDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        expense={selectedExpense}
        onApprove={handleApprove}
        onReject={handleReject}
      />
    </div>
  );
};

export default FinanceApprovals;
