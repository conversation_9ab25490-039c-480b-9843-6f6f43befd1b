import { createRoute, createRootRoute, redirect } from "@tanstack/react-router";
import AuthPageLayout from "../components/layout/AuthPageLayout";
import MainLayout from "../components/layout/MainLayout";
import ProfileLayout from "../components/layout/ProfileLayout";
import AccountInformation from "../pages/accountInformation/AccountInformation";
import Home from "../pages/home/<USER>";
import Login from "../pages/login/Login";
import NotFound from "../pages/notFound/NotFound";
import Notifications from "../pages/notifications";
import PrivacyPolicy from "../pages/PrivacyPolicy/PrivacyPolicy";
import ProfileSettings from "../pages/profile/profileSettings";
import Support from "../pages/support/supportFAQ";
import { isAuthenticated, getCurrentUser } from "../hooks/useAuth";
import { UserRole } from "../types/auth";
import LoadingSpinner from "../components/ui/LoadingSpinner";
import ErrorDisplay from "../components/ui/ErrorDisplay";
import ApprovalsContainer from "../components/approvals/ApprovalsContainer";
import Users from "../pages/users/users";
import MyTeam from "../pages/myTeam/myTeam";
import InsightsContainer from "../components/insights/InsightsContainer";
import ReportDetails from "../pages/ReportDetails/ReportDetails";

// Fix the TypeScript lint error by using proper search type
type EmptySearch = Record<string, never>;

// Enhanced authentication helper
const requireAuth = (meta?: { allowedRoles?: UserRole[]; title?: string }) => {
  return () => {
    // Check authentication
    if (!isAuthenticated()) {
      throw redirect({
        to: "/auth/login",
        search: { redirect: location.pathname },
      });
    }

    // Verify user exists
    const user = getCurrentUser();
    if (!user) {
      throw redirect({ to: "/auth/login" });
    }

    // Check role permissions
    if (meta?.allowedRoles && !meta.allowedRoles.includes(user.role)) {
      throw redirect({ to: "/unauthorized" });
    }

    // Set document title if provided
    if (meta?.title) {
      document.title = `${meta.title} | EXPENSO`;
    }
  };
};

// Root route with error boundary and not found handling
export const rootRoute = createRootRoute({
  component: MainLayout,
  notFoundComponent: NotFound,
  pendingComponent: LoadingSpinner,
  errorComponent: ({ error }) => <ErrorDisplay error={error} />,
});

// Authentication layout route
export const authLayoutRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/auth",
  component: AuthPageLayout,
  beforeLoad: () => {
    if (isAuthenticated()) {
      throw redirect({ to: "/" });
    }
  },
});

// Login route
export const loginRoute = createRoute({
  getParentRoute: () => authLayoutRoute,
  path: "/login",
  component: Login,
});

// Unauthorized access route
export const unauthorizedRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/unauthorized",
  component: () => (
    <div className="flex flex-col items-center justify-center h-screen">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
      <p className="text-gray-600 mb-4">
        You don't have permission to access this page.
      </p>
      <button
        onClick={() => window.history.back()}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Go Back
      </button>
    </div>
  ),
});

// Home route (all authenticated users)
export const homeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: Home,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
    title: "Dashboard",
  }),
});

// Insights route (Manager and Finance only)
export const insightsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/insights",
  component: InsightsContainer,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.FINANCE],
    title: "Insights",
  }),
});

// Notifications route (all authenticated users)
export const notificationsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/notifications",
  component: Notifications,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
    title: "Notifications",
  }),
});

// Finance routes group
export const financeRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/finance",
  component: () => <div>Finance Dashboard</div>,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.FINANCE],
    title: "Finance",
  }),
});

// Management routes group
export const managementRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/management",
  component: () => <div>Management Dashboard</div>,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.MANAGER],
    title: "Management",
  }),
});
// my team for manager routes group
export const myTeam = createRoute({
  getParentRoute: () => rootRoute,
  path: "/my-team",
  component: MyTeam,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.MANAGER],
    title: "Management",
  }),
});

// user for finance team routes group
export const users = createRoute({
  getParentRoute: () => rootRoute,
  path: "/users",
  component: Users,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.FINANCE],
    title: "Management",
  }),
});

// user details route
export const userDetailsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/users/$id",
  component: ReportDetails,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.FINANCE],
    title: "User Details",
  }),
});

// manageApprovals routes group
export const manageApprovalsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/approvals",
  component: ApprovalsContainer,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.MANAGER, UserRole.FINANCE],
    title: "Approvals",
  }),
});

// Profile layout route
export const profileLayoutRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/profile",
  component: ProfileLayout,
  beforeLoad: requireAuth({
    allowedRoles: [UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.FINANCE],
    title: "Profile",
  }),
});

// Profile sub-routes
export const profileSettingsRoute = createRoute({
  getParentRoute: () => profileLayoutRoute,
  path: "/",
  component: ProfileSettings,
});

export const accountInfoRoute = createRoute({
  getParentRoute: () => profileLayoutRoute,
  path: "account-information",
  validateSearch: (search: EmptySearch): EmptySearch => search,
  component: AccountInformation,
});

export const privacyPolicyRoute = createRoute({
  getParentRoute: () => profileLayoutRoute,
  path: "privacy-policy",
  validateSearch: (search: EmptySearch): EmptySearch => search,
  component: PrivacyPolicy,
});

export const supportRoute = createRoute({
  getParentRoute: () => profileLayoutRoute,
  path: "support",
  validateSearch: (search: EmptySearch): EmptySearch => search,
  component: Support,
});

// Route tree construction
export const routeTree = rootRoute.addChildren([
  homeRoute,
  insightsRoute,
  notificationsRoute,
  financeRoute,
  managementRoute,
  unauthorizedRoute,
  manageApprovalsRoute,
  myTeam,
  users,
  userDetailsRoute,
  authLayoutRoute.addChildren([loginRoute]),
  profileLayoutRoute.addChildren([
    profileSettingsRoute,
    accountInfoRoute,
    privacyPolicyRoute,
    supportRoute,
  ]),
]);
