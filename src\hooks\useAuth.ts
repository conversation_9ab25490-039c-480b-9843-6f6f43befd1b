

// import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// import { AuthUtils } from '../utils/auth'
// import { UserRole, type AuthState, type User } from '../types/auth'

// // =============================================
// // DUMMY CREDENTIALS STORAGE
// // =============================================

// type DummyUser = User & {
//     password: string
// }

// // Pre-defined dummy users with different roles
// const DUMMY_USERS: DummyUser[] = [
//     {
//         id: '1',
//         email: '<EMAIL>',
//         name: 'John Employee',
//         password: 'employee123',
//         role: UserRole.EMPLOYEE
//     },
//     {
//         id: '2',
//         email: '<EMAIL>',
//         name: 'Sarah Manager',
//         password: 'manager123',
//         role: UserRole.MANAGER
//     },
//     {
//         id: '3',
//         email: '<EMAIL>',
//         name: 'Mike Finance',
//         password: 'finance123',
//         role: UserRole.FINANCE
//     },

// ]

// // =============================================
// // MOCK AUTH STORAGE
// // =============================================

// // Session storage key
// const AUTH_STORAGE_KEY = 'mock-jwt-token'

// // Initialize auth state from session storage or create fresh
// const loadAuthState = (): AuthState => {
//     if (typeof window === 'undefined') {
//         return {
//             user: null,
//             isAuthenticated: false,
//             isLoading: false,
//             token: null,
//         }
//     }

//     const storedState = sessionStorage.getItem(AUTH_STORAGE_KEY)
//     return storedState
//         ? JSON.parse(storedState)
//         : {
//             user: null,
//             isAuthenticated: false,
//             isLoading: false,
//             token: null,
//         }
// }

// // Save auth state to session storage
// const saveAuthState = (state: AuthState) => {
//     if (typeof window !== 'undefined') {
//         sessionStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state))
//     }
// }

// // Clear auth state from session storage
// const clearAuthState = () => {
//     if (typeof window !== 'undefined') {
//         sessionStorage.removeItem(AUTH_STORAGE_KEY)
//     }
// }

// // Global auth state
// let globalAuthState: AuthState = loadAuthState()

// // =============================================
// // MOCK API FUNCTIONS
// // =============================================

// const authApi = {
//     getCurrentUser: async (): Promise<User | null> => {
//         return globalAuthState.user
//     },

//     login: async (credentials: { email: string; password: string }): Promise<{ user: User; token: string }> => {
//         return new Promise((resolve, reject) => {
//             setTimeout(() => {
//                 // Find user in dummy storage
//                 const user = DUMMY_USERS.find(
//                     u => u.email === credentials.email && u.password === credentials.password
//                 )

//                 if (user) {
//                     // Clone user object without password
//                     const { ...userWithoutPassword } = user

//                     const token = `mock-jwt-token-${Date.now()}-${user.id}`

//                     // Update global state
//                     globalAuthState = {
//                         user: userWithoutPassword,
//                         isAuthenticated: true,
//                         isLoading: false,
//                         token,
//                     }

//                     // Persist to session storage
//                     saveAuthState(globalAuthState)

//                     resolve({ user: userWithoutPassword, token })
//                 } else {
//                     reject(new Error('Invalid email or password'))
//                 }
//             }, 800) // Simulate network delay
//         })
//     },

//     logout: async (): Promise<void> => {
//         return new Promise((resolve) => {
//             setTimeout(() => {
//                 // Clear global state
//                 globalAuthState = {
//                     user: null,
//                     isAuthenticated: false,
//                     isLoading: false,
//                     token: null,
//                 }

//                 // Clear from session storage
//                 clearAuthState()
//                 window.location.href = '/auth/login'

//                 resolve()
//             }, 200)
//         })
//     },

//     // Optional: Mock function to simulate token refresh
//     refreshToken: async (): Promise<{ user: User; token: string }> => {
//         return new Promise((resolve, reject) => {
//             setTimeout(() => {
//                 if (globalAuthState.user) {
//                     const newToken = `mock-jwt-token-refreshed-${Date.now()}-${globalAuthState.user.id}`

//                     globalAuthState = {
//                         ...globalAuthState,
//                         token: newToken,
//                     }

//                     saveAuthState(globalAuthState)

//                     // resolve({ user: globalAuthState.user, token: newToken })
//                     resolve({ user: globalAuthState.user, token: newToken })
//                 } else {
//                     reject(new Error('No user logged in'))
//                 }
//             }, 500)
//         })
//     }
// }

// // =============================================
// // AUTH HOOK
// // =============================================

// export const useAuth = () => {
//     const queryClient = useQueryClient()

//     // Query to get current user
//     const {
//         data: user,
//         isLoading,
//         isError,
//     } = useQuery({
//         queryKey: ['currentUser'],
//         queryFn: authApi.getCurrentUser,
//         staleTime: 5 * 60 * 1000, // 5 minutes
//         gcTime: 10 * 60 * 1000, // 10 minutes
//         initialData: globalAuthState.user,
//     })

//     // Login mutation
//     const loginMutation = useMutation({
//         mutationFn: authApi.login,
//         onSuccess: (data) => {
//             globalAuthState = {
//                 user: data.user,
//                 isAuthenticated: true,
//                 isLoading: false,
//                 token: data.token,
//             }
//             queryClient.setQueryData(['currentUser'], data.user)
//         },
//     })



//     // Logout mutation
//     const logoutMutation = useMutation({
//         mutationFn: authApi.logout,
//         onSuccess: () => {
//             globalAuthState = {
//                 user: null,
//                 isAuthenticated: false,
//                 isLoading: false,
//                 token: null,
//             }
//             queryClient.setQueryData(['currentUser'], null)
//             queryClient.clear()
//         },
//     })

//     // Optional: Token refresh mutation
//     const refreshTokenMutation = useMutation({
//         mutationFn: authApi.refreshToken,
//         onSuccess: (data) => {
//             globalAuthState = {
//                 ...globalAuthState,
//                 token: data.token,
//             }
//             queryClient.setQueryData(['currentUser'], data.user)
//         },
//     })

//     // Helper functions
//     const hasRole = (role: UserRole) => AuthUtils.hasRole(user, role)
//     const hasAnyRole = (roles: UserRole[]) => AuthUtils.hasAnyRole(user, roles)
//     const canAccessRoute = (routePath: string) => AuthUtils.canAccessRoute(user, routePath)

//     return {
//         // User data
//         user,
//         isAuthenticated: !!user,
//         isLoading,
//         isError,
//         token: globalAuthState.token,

//         // Actions
//         login: loginMutation.mutate,
//         logout: logoutMutation.mutate,
//         refreshToken: refreshTokenMutation.mutate,
//         isLoggingIn: loginMutation.isPending,
//         isLoggingOut: logoutMutation.isPending,
//         isRefreshingToken: refreshTokenMutation.isPending,

//         // Role checks
//         hasRole,
//         hasAnyRole,
//         canAccessRoute,

//         // Direct role checks
//         isEmployee: hasRole(UserRole.EMPLOYEE),
//         isManager: hasRole(UserRole.MANAGER),
//         isFinance: hasRole(UserRole.FINANCE),
//     }
// }

// // =============================================
// // EXPORTED UTILITY FUNCTIONS
// // =============================================

// export const isAuthenticated = (): boolean => {
//     return globalAuthState.isAuthenticated && !!globalAuthState.user
// }

// export const getCurrentUser = (): User | null => {
//     return globalAuthState.user
// }

// export const getAuthToken = (): string | null => {
//     return globalAuthState.token
// }

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import { AuthUtils } from '../utils/auth'
import { UserRole, type AuthState, type User } from '../types/auth'

// =============================================
// DUMMY CREDENTIALS STORAGE
// =============================================

type DummyUser = User & {
    password: string
}

// Pre-defined dummy users with different roles
const DUMMY_USERS: DummyUser[] = [
    {
        id: '1',
        email: '<EMAIL>',
        name: 'John Employee',
        password: 'employee123',
        role: UserRole.EMPLOYEE
    },
    {
        id: '2',
        email: '<EMAIL>',
        name: 'Sarah Manager',
        password: 'manager123',
        role: UserRole.MANAGER
    },
    {
        id: '3',
        email: '<EMAIL>',
        name: 'Mike Finance',
        password: 'finance123',
        role: UserRole.FINANCE
    },

]

// =============================================
// MOCK AUTH STORAGE
// =============================================

// Session storage key
const AUTH_STORAGE_KEY = 'mock-jwt-token'

// Initialize auth state from session storage or create fresh
const loadAuthState = (): AuthState => {
    if (typeof window === 'undefined') {
        return {
            user: null,
            isAuthenticated: false,
            isLoading: false,
            token: null,
        }
    }

    const storedState = sessionStorage.getItem(AUTH_STORAGE_KEY)
    return storedState
        ? JSON.parse(storedState)
        : {
            user: null,
            isAuthenticated: false,
            isLoading: false,
            token: null,
        }
}

// Save auth state to session storage
const saveAuthState = (state: AuthState) => {
    if (typeof window !== 'undefined') {
        sessionStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(state))
    }
}

// Clear auth state from session storage
const clearAuthState = () => {
    if (typeof window !== 'undefined') {
        sessionStorage.removeItem(AUTH_STORAGE_KEY)
    }
}

// Global auth state
let globalAuthState: AuthState = loadAuthState()

// =============================================
// MOCK API FUNCTIONS
// =============================================

const authApi = {
    getCurrentUser: async (): Promise<User | null> => {
        return globalAuthState.user
    },

    login: async (credentials: { email: string; password: string }): Promise<{ user: User; token: string }> => {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Find user in dummy storage
                const user = DUMMY_USERS.find(
                    u => u.email === credentials.email && u.password === credentials.password
                )

                if (user) {
                    // Clone user object without password
                    const { ...userWithoutPassword } = user

                    const token = `mock-jwt-token-${Date.now()}-${user.id}`

                    // Update global state
                    globalAuthState = {
                        user: userWithoutPassword,
                        isAuthenticated: true,
                        isLoading: false,
                        token,
                    }

                    // Persist to session storage
                    saveAuthState(globalAuthState)

                    resolve({ user: userWithoutPassword, token })
                } else {
                    reject(new Error('Invalid email or password'))
                }
            }, 800) // Simulate network delay
        })
    },

    logout: async (): Promise<void> => {
        return new Promise((resolve) => {
            setTimeout(() => {
                // Clear global state
                globalAuthState = {
                    user: null,
                    isAuthenticated: false,
                    isLoading: false,
                    token: null,
                }

                // Clear from session storage
                clearAuthState()
                window.location.href = '/auth/login'

                resolve()
            }, 200)
        })
    },

    // Optional: Mock function to simulate token refresh
    refreshToken: async (): Promise<{ user: User; token: string }> => {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (globalAuthState.user) {
                    const newToken = `mock-jwt-token-refreshed-${Date.now()}-${globalAuthState.user.id}`

                    globalAuthState = {
                        ...globalAuthState,
                        token: newToken,
                    }

                    saveAuthState(globalAuthState)

                    resolve({ user: globalAuthState.user!, token: newToken })
                } else {
                    reject(new Error('No user logged in'))
                }
            }, 500)
        })
    }
}

// =============================================
// AUTH HOOK
// =============================================

export const useAuth = () => {
    const queryClient = useQueryClient()

    // Query to get current user
    const {
        data: user,
        isLoading,
        isError,
    } = useQuery({
        queryKey: ['currentUser'],
        queryFn: authApi.getCurrentUser,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        initialData: globalAuthState.user,
    })

    // Login mutation
    const loginMutation = useMutation({
        mutationFn: authApi.login,
        onSuccess: (data) => {
            globalAuthState = {
                user: data.user,
                isAuthenticated: true,
                isLoading: false,
                token: data.token,
            }
            queryClient.setQueryData(['currentUser'], data.user)
        },
    })



    // Logout mutation
    const logoutMutation = useMutation({
        mutationFn: authApi.logout,
        onSuccess: () => {
            globalAuthState = {
                user: null,
                isAuthenticated: false,
                isLoading: false,
                token: null,
            }
            queryClient.setQueryData(['currentUser'], null)
            queryClient.clear()
        },
    })

    // Optional: Token refresh mutation
    const refreshTokenMutation = useMutation({
        mutationFn: authApi.refreshToken,
        onSuccess: (data) => {
            globalAuthState = {
                ...globalAuthState,
                token: data.token,
            }
            queryClient.setQueryData(['currentUser'], data.user)
        },
    })

    // Helper functions
    const hasRole = (role: UserRole) => AuthUtils.hasRole(user, role)
    const hasAnyRole = (roles: UserRole[]) => AuthUtils.hasAnyRole(user, roles)
    const canAccessRoute = (routePath: string) => AuthUtils.canAccessRoute(user, routePath)

    return {
        // User data
        user,
        isAuthenticated: !!user,
        isLoading,
        isError,
        token: globalAuthState.token,

        // Actions
        login: loginMutation.mutate,
        logout: logoutMutation.mutate,
        refreshToken: refreshTokenMutation.mutate,
        isLoggingIn: loginMutation.isPending,
        isLoggingOut: logoutMutation.isPending,
        isRefreshingToken: refreshTokenMutation.isPending,

        // Role checks
        hasRole,
        hasAnyRole,
        canAccessRoute,

        // Direct role checks
        isEmployee: hasRole(UserRole.EMPLOYEE),
        isManager: hasRole(UserRole.MANAGER),
        isFinance: hasRole(UserRole.FINANCE),
    }
}

// =============================================
// EXPORTED UTILITY FUNCTIONS
// =============================================

export const isAuthenticated = (): boolean => {
    return globalAuthState.isAuthenticated && !!globalAuthState.user
}

export const getCurrentUser = (): User | null => {
    return globalAuthState.user
}

export const getAuthToken = (): string | null => {
    return globalAuthState.token
}