import React, { useState } from "react";
import { AlertTriangle, CheckCircle } from "lucide-react";
import ExpenseRejectionModal from "./ExpenseRejectionModal";
import ExpenseApprovalSuccessModal from "./ExpenseApprovalSuccessModal";
// Import your custom SVG files as URLs
import mealsIcon from "/burgerIcon.svg";
import travelIcon from "/palneIcon.svg";
import categoryIcon from "/categoryIcon.svg";
import defaultIcon from "/categoryIcon.svg";
import { IoLayers } from "react-icons/io5";

// Category icons mapping with image sources
const categoryIcons: Record<string, string> = {
  Meals: mealsIcon,
  Travel: travelIcon,
  Category: categoryIcon,
};

// Default icon fallback
const getDefaultIcon = () => defaultIcon;

// Types
interface ExpenseItem {
  id: string;
  category: string;
  title: string;
  amount: number;
  date: string;
  description: string;
  uploads: string[];
  markedForRejection: boolean;
}

interface ExpenseRequest {
  id: string;
  employee: string;
  expenseName: string;
  category: string;
  submissionDate: string;
  amount: number;
  isMultiple: boolean;
  expenses?: ExpenseItem[];
  isCompliant: boolean;
  reason?: string;
}

interface ExpenseDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  expense: ExpenseRequest | null;
  onApprove: (id: string) => void;
  onReject: (id: string) => void;
}

const ExpenseDetailsModal: React.FC<ExpenseDetailsModalProps> = ({
  isOpen,
  onClose,
  expense,
  onApprove,
  onReject,
}) => {
  const [rejectionToggles, setRejectionToggles] = useState<{
    [key: string]: boolean;
  }>({});
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);

  if (!isOpen || !expense) return null;

  const toggleRejection = (expenseId: string) => {
    setRejectionToggles((prev) => ({
      ...prev,
      [expenseId]: !prev[expenseId],
    }));
  };

  const handleReject = () => {
    setShowRejectionModal(true);
  };

  const handleApprove = () => {
    onApprove(expense.id);
    setShowApprovalModal(true);
  };

  const totalAmount =
    expense.expenses?.reduce((sum, exp) => sum + exp.amount, 0) ||
    expense.amount;

  const handleSubmitRejection = (data: {
    expenseId: string;
    reasons: string[];
    comment: string;
  }) => {
    onReject(data.expenseId);
    setShowRejectionModal(false);
  };

  const handleUndoApproval = () => {
    setShowApprovalModal(false);
  };

  const handleUndoRejection = () => {
    setShowRejectionModal(false);
  };

  const handleBackToHome = () => {
    onClose();
  };

  return (
    <>
      <div className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center z-50 animate-fadeIn">
        <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden shadow-2xl animate-slideUp">
          {/* Header */}
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Expense Details
            </h2>
            <button
              onClick={onClose}
              className="text-red-500 cursor-pointer hover:text-red-700 font-medium transition-colors"
            >
              Close
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[70vh]">
            {/* Submission Info */}
            <div className="mb-6">
              <p className="text-[Slategray] mb-1">
                Submitted by <span className="font-medium">{expense.employee}</span> on
              </p>
              <p className="text-[Slategray]">
                {new Date(expense.submissionDate).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </p>
            </div>

            {/* Main Expense Header */}
            <div className="rounded-lg mb-6 flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-gradient-to-br from-cyan-400 to-green-400">
                <IoLayers className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-lg font-medium text-[Darkturquoise]">
                {expense.expenseName}
              </h3>
            </div>

            {/* Individual Expenses */}
            {expense.expenses?.map((exp) => (
              <div
                key={exp.id}
                className="mb-6 border-b border-gray-200 pb-6 last:border-b-0"
              >
                {/* Expense Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                      <img
                        src={categoryIcons[exp.category] || getDefaultIcon()}
                        alt={exp.category}
                        className="w-5 h-5"
                      />
                    </div>
                    <div>
                      <span className="text-sm text-slate-600 font-bold">
                        {exp.category}
                      </span>
                      <span className="text-sm text-slate-600 ml-2">
                        {exp.date}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className="text-sm text-[#5C738A] font-light">
                      Mark for rejection
                    </span>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={
                          rejectionToggles[exp.id] || exp.markedForRejection
                        }
                        onChange={() => toggleRejection(exp.id)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                    </label>
                  </div>
                </div>

                {/* Expense Details */}
                <div className="flex items-center align-middle justify-between">
                  <h4 className="font-semibold text-gray-900 text-lg mb-2">
                    {exp.title}
                  </h4>
                  <div className="flex justify-between items-center rounded-xl p-1 px-6 mb-4 bg-[#F2F2F5]">
                    <span className="text-sm font-semibold text-[#5C738A]">
                      ${exp.amount.toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-4">
                  <h5 className="font-medium text-[#B6BEC5] mb-2">
                    Description
                  </h5>
                  <p className="text-gray-600 text-sm leading-relaxed shadow-custom-desc rounded bg-white p-2">
                    {exp.description}
                  </p>
                </div>

                {/* Uploads */}
                <div>
                  <h5 className="font-medium text-gray-700 mb-3">Uploads</h5>
                  {exp.uploads.length > 0 ? (
                    <div className="grid grid-cols-5 gap-3">
                      {exp.uploads.map((img, uploadIndex) => (
                        <img
                          key={uploadIndex}
                          src={img}
                          alt={`upload-${uploadIndex}`}
                          className="w-28 h-28 object-cover rounded shadow"
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 rounded text-[#5C738A] bg-[#F2F5F8]">
                      <p>No files uploaded</p>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* Compliance Status and Total Amount */}
            {expense.isCompliant ? (
              /* Compliant State - Green Theme */
              <div className="bg-green-50 border border-green-100 rounded-xl px-5 py-4 mb-6">
                <div className="flex justify-between align-middle">
                  <div className="flex items-center gap-2">
                    <div className="w-9 h-9 flex items-center justify-center border border-green-300 rounded-md ">
                      <CheckCircle className="w-5 h-5 text-[#00BE5C]" />
                    </div>
                    <p className="text-[#00BE5C] text-sm font-medium">
                      Complies with company policy
                    </p>
                  </div>

                  {/* Total Amount */}
                  <div className="text-left border border-white rounded-md px-4 py-2">
                    <p className="text-xs text-gray-500 font-medium mb-1">
                      Total Amount
                    </p>
                    <p className="text-2xl font-semibold text-[Slategray]">
                      $
                      {totalAmount.toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                      })}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              /* Non-Compliant State - Orange Theme */
              <div className="bg-orange-50 border border-orange-100 rounded-xl px-5 py-4 mb-6">
                <div className="flex justify-between align-middle">
                  <div className="flex items-center gap-2">
                    <div className="w-9 h-9 flex items-center justify-center border border-orange-300 rounded-md">
                      <AlertTriangle className="w-5 h-5 text-[#FF9811]" />
                    </div>
                    <p className="text-[#FF9811] text-sm font-medium">
                      Not compliant with company policy
                    </p>
                  </div>

                  {/* Total Amount */}
                  <div className="text-left border border-white rounded-md px-4 py-2">
                  <p className="text-xs text-gray-500 font-medium mb-1">
                    Total Amount
                  </p>
                  <p className="text-2xl font-semibold text-[Slategray]">
                    $
                    {totalAmount.toLocaleString("en-US", {
                      minimumFractionDigits: 2,
                    })}
                  </p>
                </div>
              </div>
                {/* Reason for non-compliance */}
                {expense.reason && (
                  <p className="mt-4 text-sm text-slate-600">
                    <span className="font-semibold text-slate-600">Reason:</span>{" "}
                    {expense.reason}
                  </p>
                )}
              </div>
            )}

            {/* Footer Actions */}
            <div className="flex justify-end gap-4">
              <button
                onClick={handleReject}
                className="custom-reject-btn"
              >
                Reject
              </button>
              <button
                onClick={handleApprove}
                className="custom-approve-btn"
              >
                Approve
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Rejection Modal */}
      <ExpenseRejectionModal
        isOpen={showRejectionModal}
        onClose={() => setShowRejectionModal(false)}
        handleUndoRejection={handleUndoRejection}
        expense={expense}
        onSubmitRejection={handleSubmitRejection}
      />

      {/* Approval Success Modal */}
      <ExpenseApprovalSuccessModal
        isOpen={showApprovalModal}
        onClose={() => setShowApprovalModal(false)}
        employeeName={expense.employee}
        onUndoApproval={handleUndoApproval}
        onBackToHome={handleBackToHome}
      />
    </>
  );
};

export default ExpenseDetailsModal;