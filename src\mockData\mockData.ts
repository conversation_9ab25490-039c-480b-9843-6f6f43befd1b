// Mock data for disputes
export interface SampleDispute {
  id: string;
  expenseType: string;
  raisedOn: string;
  amount: string;
  status: string;
  approvalStatus: string;
  iconType: 'file' | 'document';
}

export const sampleDisputes: SampleDispute[] = [
  {
    id: '1',
    expenseType: 'Travel & Transport',
    raisedOn: '2024-07-15',
    amount: '$1,200.00',
    status: 'Under Review',
    approvalStatus: 'Pending',
    iconType: 'file'
  },
  {
    id: '2',
    expenseType: 'Meals & Entertainment',
    raisedOn: '2024-07-12',
    amount: '$450.00',
    status: 'Resolved',
    approvalStatus: 'All Approved',
    iconType: 'document'
  },
  {
    id: '3',
    expenseType: 'Office Supplies',
    raisedOn: '2024-07-10',
    amount: '$320.00',
    status: 'Escalated',
    approvalStatus: 'Rejected',
    iconType: 'file'
  },
  {
    id: '4',
    expenseType: 'Professional Development',
    raisedOn: '2024-07-08',
    amount: '$2,500.00',
    status: 'Under Review',
    approvalStatus: 'Pending',
    iconType: 'document'
  },
  {
    id: '5',
    expenseType: 'Equipment',
    raisedOn: '2024-07-05',
    amount: '$800.00',
    status: 'Resolved',
    approvalStatus: 'All Approved',
    iconType: 'file'
  }
];