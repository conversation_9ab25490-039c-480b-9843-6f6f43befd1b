import { useState } from "react";
import MyInsights from "../../pages/myInsights/myInsights";
import TeamInsights from "../../pages/teamInsights/teamInsights";

type TabOption = "my-insights" | "team-insights";

const InsightsContainer: React.FC = () => {
  const [tab, setTab] = useState<TabOption>("my-insights");

  return (
    <div className="mx-auto mt-3 min-h-screen px-4">
      {/* Toggle Button Group */}
      <div className="flex justify-center mt-4 mb-8">
        <div className="relative bg-teal-600 rounded-full p-1 shadow-lg w-full max-w-4xl flex">
          <button
            onClick={() => setTab("my-insights")}
            className={`w-1/2 px-4 py-2 text-sm cursor-pointer font-semibold rounded-full transition-all duration-300 ease-in-out ${
              tab === "my-insights" ? "bg-white text-teal-600" : "text-white"
            }`}
          >
            My Insights
          </button>
          <button
            onClick={() => setTab("team-insights")}
            className={`w-1/2 px-4 py-2 text-sm cursor-pointer font-semibold rounded-full transition-all duration-300 ease-in-out ${
              tab === "team-insights" ? "bg-white text-teal-600" : "text-white"
            }`}
          >
            Team Insights
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="bg-white rounded-xl shadow-lg relative min-h-[200px]">
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            tab === "my-insights"
              ? "opacity-100 z-10"
              : "opacity-0 z-0 pointer-events-none"
          }`}
        >
          <MyInsights />
        </div>

        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            tab === "team-insights"
              ? "opacity-100 z-10"
              : "opacity-0 z-0 pointer-events-none"
          }`}
        >
          <TeamInsights />
        </div>
      </div>
    </div>
  );
};

export default InsightsContainer;
