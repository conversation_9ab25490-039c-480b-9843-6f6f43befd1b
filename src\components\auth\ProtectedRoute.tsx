// import React from "react";
// import { useAuth } from "../../hooks/useAuth";
// import { UserRole } from "../../types/auth";
// import { Navigate, useLocation } from "@tanstack/react-router";

// interface ProtectedRouteProps {
//   children: React.ReactNode;
//   roles?: UserRole[];
//   fallback?: React.ReactNode;
//   redirectTo?: string;
//   loadingComponent?: React.ReactNode;
// }

// export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
//   children,
//   roles,
//   fallback = <DefaultFallback />,
//   redirectTo = "/auth/login",
//   loadingComponent = <DefaultLoading />,
// }) => {
//   const { isAuthenticated, isLoading, user, hasAnyRole } = useAuth();
//   const location = useLocation();

//   if (isLoading) {
//     return <>{loadingComponent}</>;
//   }

//   if (!isAuthenticated || !user) {
//     return (
//       <Navigate
//         to={redirectTo}
//         search={{ redirect: location.pathname }}
//         replace
//       />
//     );
//   }

//   if (roles && !hasAnyRole(roles)) {
//     return <>{fallback}</>;
//   }

//   return <>{children}</>;
// };

// const DefaultLoading = () => (
//   <div className="flex justify-center items-center h-screen">
//     <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0ee6c9]"></div>
//   </div>
// );

// const DefaultFallback = () => (
//   <div className="p-4 text-red-500 text-center">
//     <h2>Access Denied</h2>
//     <p>You don't have permission to view this page.</p>
//   </div>
// );

// // Enhanced ConditionalRender with memoization
// export const ConditionalRender: React.FC<{
//   children: React.ReactNode;
//   roles?: UserRole[];
//   fallback?: React.ReactNode;
// }> = React.memo(({ children, roles, fallback = null }) => {
//   const { hasAnyRole } = useAuth();

//   if (roles && !hasAnyRole(roles)) {
//     return <>{fallback}</>;
//   }

//   return <>{children}</>;
// });

// // Role-specific components with proper typing
// type RoleComponentProps = {
//   children: React.ReactNode;
//   fallback?: React.ReactNode;
// };

// const createRoleComponent = (roles: UserRole[]) => {
//   return React.memo(({ children, fallback }: RoleComponentProps) => (
//     <ConditionalRender roles={roles} fallback={fallback}>
//       {children}
//     </ConditionalRender>
//   ));
// };

// export const EmployeeOnly = createRoleComponent([UserRole.EMPLOYEE]);
// export const ManagerOnly = createRoleComponent([UserRole.MANAGER]);
// export const FinanceOnly = createRoleComponent([UserRole.FINANCE]);
// export const ManagerOrFinance = createRoleComponent([
//   UserRole.MANAGER,
//   UserRole.FINANCE,
// ]);
import React from "react";
import { useAuth } from "../../hooks/useAuth";
import { UserRole } from "../../types/auth";
import { Navigate } from "@tanstack/react-router";

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: UserRole[];
  fallback?: React.ReactNode;
  redirectTo?: string;
  loadingComponent?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  roles,
  fallback = <DefaultFallback />,
  redirectTo = "/auth/login",
  loadingComponent = <DefaultLoading />,
}) => {
  const { isAuthenticated, isLoading, user, hasAnyRole } = useAuth();
  // const location = useLocation();

  if (isLoading) {
    return <>{loadingComponent}</>;
  }

  if (!isAuthenticated || !user) {
    return <Navigate to={redirectTo} replace />;
  }

  if (roles && !hasAnyRole(roles)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

const DefaultLoading = () => (
  <div className="flex justify-center items-center h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#0ee6c9]"></div>
  </div>
);

const DefaultFallback = () => (
  <div className="p-4 text-red-500 text-center">
    <h2>Access Denied</h2>
    <p>You don't have permission to view this page.</p>
  </div>
);

// Enhanced ConditionalRender with memoization
export const ConditionalRender: React.FC<{
  children: React.ReactNode;
  roles?: UserRole[];
  fallback?: React.ReactNode;
}> = React.memo(({ children, roles, fallback = null }) => {
  const { hasAnyRole } = useAuth();

  if (roles && !hasAnyRole(roles)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
});

// Role-specific components with proper typing
type RoleComponentProps = {
  children: React.ReactNode;
  fallback?: React.ReactNode;
};

const createRoleComponent = (roles: UserRole[]) => {
  return React.memo(({ children, fallback }: RoleComponentProps) => (
    <ConditionalRender roles={roles} fallback={fallback}>
      {children}
    </ConditionalRender>
  ));
};

export const EmployeeOnly = createRoleComponent([UserRole.EMPLOYEE]);
export const ManagerOnly = createRoleComponent([UserRole.MANAGER]);
export const FinanceOnly = createRoleComponent([UserRole.FINANCE]);
export const ManagerOrFinance = createRoleComponent([
  UserRole.MANAGER,
  UserRole.FINANCE,
]);
