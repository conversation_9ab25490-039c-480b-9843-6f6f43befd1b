// src/components/layout/AuthPageLayout.tsx
import { Outlet } from "@tanstack/react-router";
import type { ReactNode } from "react";

interface AuthPageLayoutProps {
  children?: ReactNode;
}

const AuthPageLayout = ({ children }: AuthPageLayoutProps) => {
  const content = children ?? <Outlet />;

  return (
    <main className="relative flex flex-col h-screen bg-white">
      <div className="flex-1 brand-gradient rounded-b-2xl">{content}</div>
      <div className="flex-1"></div>
      <div className="absolute top-1/2 right-0 -translate-y-1/2 z-10">
        <img
          src="/authPageSvg.svg"
          alt="auth page svg"
          className="object-contain"
        />
      </div>
    </main>
  );
};

export default AuthPageLayout;
