@import "tailwindcss";

@theme {
  /* Custom colors - Blue palette */
  --color-primary: #3b82f6;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Override default red colors with blue */
  --color-red-50: #eff6ff;
  --color-red-100: #dbeafe;
  --color-red-200: #bfdbfe;
  --color-red-300: #93c5fd;
  --color-red-400: #60a5fa;
  --color-red-500: #3b82f6;
  --color-red-600: #2563eb;
  --color-red-700: #1d4ed8;
  --color-red-800: #1e40af;
  --color-red-900: #1e3a8a;

  /* Custom colors - cyan palette */
  --color-green-449: #00ffaa;

  /* Custom background gradients */
  --color-custom-gradient-from: #667eea;
  --color-custom-gradient-to: #764ba2;

  /* Custom project colors  */
  --color-green-350: #037878;
}

/* Custom utility classes */
.bg-custom-nav {
  background: linear-gradient(
    270deg,
    #0f2027 -9.2%,
    #203a43 47.44%,
    #2c5364 104.07%
  );
  height: 70px;
}

/* Custom shadows */
.shadow-custom {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Custom animations */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}
/* Custom Buttons */
.custom-cyan-btn {
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  border: 2px solid transparent;
  background: linear-gradient(
    46deg,
    #2adada -9.59%,
    #18bfa0 41.61%,
    #00d3c5 96.92%
  );
  color: #fff;
  box-shadow: 0px 4px 19px 0px rgba(194, 235, 235, 0.19);
}
.custom-cyan-btn:hover {
  border-color: #34d399;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors shadow-primary;
}

.btn-outline {
  @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white px-6 py-2 rounded-lg transition-colors;
}


