import { Link, useRouter } from "@tanstack/react-router";
import { useState } from "react";

import ExpenseModal from "../common/ExpenseModal";
import SuccessModal from "../common/SuccessModal";
import { useAuth } from "../../hooks/useAuth";
import { UserRole } from "../../types/auth";

type ExpenseFormData = {
  name: string;
  amount: number;
  category: string;
  description: string;
  receipts: { file: File; preview: string }[];
};

export default function Navbar() {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [expenses, setExpenses] = useState<ExpenseFormData[]>([]);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState<boolean>(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const router = useRouter();
  const currentPath = router.state.location.pathname;

  // Get auth context
  const { user, isEmployee, isManager, isFinance } = useAuth();

  const handleSuccessClose = () => {
    setIsSuccessModalOpen(false);
    setExpenses([]);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const isActive = (path: string) => currentPath === path;

  // Define navigation items based on roles
  const getNavItems = () => {
    if (!user) return [];

    switch (user.role) {
      case UserRole.EMPLOYEE:
        return [
          { name: "Home", path: "/" },
          { name: "Insights", path: "/insights" },
          { name: "Notifications", path: "/notifications" },
          { name: "Profile", path: "/profile" },
        ];

      case UserRole.MANAGER:
        return [
          { name: "Home", path: "/" },
          { name: "Approvals", path: "/approvals" },
          { name: "My Team", path: "/my-team" },
          { name: "Insights", path: "/insights" },
          { name: "Notifications", path: "/notifications" },
          { name: "Profile", path: "/profile" },
        ];

      case UserRole.FINANCE:
        return [
          { name: "Home", path: "/" },
          { name: "Approvals", path: "/approvals" },
          { name: "Users", path: "/users" },
          { name: "Insights", path: "/insights" },
          { name: "Notifications", path: "/notifications" },
          { name: "Profile", path: "/profile" },
        ];

      default:
        return [];
    }
  };

  const navItems = getNavItems();

  // Don't render navbar if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <>
      <nav className="bg-custom-nav bg-no-repeat bg-[length:100%_100%] shadow-lg sticky top-0 z-50">
        {/* Main navbar container */}
        <div className="flex items-center justify-between px-4 sm:px-6 py-1">
          {/* Logo */}
          <div className="flex items-center space-x-2 flex-shrink-0">
            <img src="brandLogo.svg" alt="Expenso" className="h-8 sm:h-10" />
          </div>

          {/* Desktop Nav Links */}
          <ul className="hidden md:flex space-x-4 lg:space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`inline-flex items-center h-full py-5 px-2 lg:px-3 text-white font-medium transition-all duration-300 border-b-4 text-sm lg:text-base ${
                  isActive(item.path)
                    ? "border-green-400 bg-[#0F2027] font-bold"
                    : "border-transparent hover:border-green-300 hover:bg-[#0F2027]"
                }`}
              >
                {item.name}
              </Link>
            ))}
          </ul>

          {/* Desktop Add Expense Button - Only show for employees and managers */}
          {(isEmployee || isManager || isFinance) && (
            <button
              type="button"
              className="hidden md:block custom-cyan-btn cursor-pointer bg-gradient-to-r from-cyan-500 to-teal-400 text-white font-semibold py-2 px-3 lg:px-4 rounded-lg shadow hover:opacity-90 transition text-sm lg:text-base"
              onClick={() => setIsModalOpen(true)}
            >
              + Add Expense
            </button>
          )}

          {/* Mobile menu button */}
          <button
            type="button"
            className="md:hidden inline-flex items-center justify-center p-2 rounded-md text-white hover:bg-[#0F2027] focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-400 transition-colors"
            onClick={toggleMobileMenu}
            aria-expanded={isMobileMenuOpen}
          >
            <svg
              className={`${isMobileMenuOpen ? "hidden" : "block"} h-6 w-6`}
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
            <svg
              className={`${isMobileMenuOpen ? "block" : "hidden"} h-6 w-6`}
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Mobile menu */}
        <div className={`md:hidden ${isMobileMenuOpen ? "block" : "hidden"}`}>
          <div className="px-2 pt-2 pb-3 space-y-1 bg-[#0F2027] bg-opacity-95">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`block px-3 py-2 rounded-md text-base font-medium transition-all duration-300 ${
                  isActive(item.path)
                    ? "text-green-400 bg-[#203A43] border-l-4 border-green-400"
                    : "text-white hover:text-green-300 hover:bg-[#203A43]"
                }`}
                onClick={closeMobileMenu}
              >
                {item.name}
              </Link>
            ))}

            {/* Mobile Add Expense Button - Only show for employees and managers */}
            {(isEmployee || isManager || isFinance) && (
              <button
                type="button"
                className="w-full mt-4 custom-cyan-btn cursor-pointer bg-gradient-to-r from-cyan-500 to-teal-400 text-white font-semibold py-2 px-4 rounded-lg shadow hover:opacity-90 transition text-center"
                onClick={() => {
                  setIsModalOpen(true);
                  closeMobileMenu();
                }}
              >
                + Add Expense
              </button>
            )}
          </div>
        </div>
      </nav>

      {(isEmployee || isManager || isFinance) && (
        <>
          <ExpenseModal
            modalOpen={isModalOpen}
            setModalOpen={setIsModalOpen}
            expenses={expenses}
            setExpenses={setExpenses}
            setIsSuccessModalOpen={setIsSuccessModalOpen}
          />
          <SuccessModal
            isOpen={isSuccessModalOpen}
            onClose={handleSuccessClose}
          />
        </>
      )}
    </>
  );
}
