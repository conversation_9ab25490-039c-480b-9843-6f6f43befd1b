// Mock data for expense reports
export interface SingleExpenseTableData {
  id: string;
  type: string;
  description: string;
  date: string;
  amount: string;
  status: 'Check Done' | 'Rejected';
  approval: 'Approved' | 'Rejected';
}

export const singleExpenseTableData: SingleExpenseTableData[] = [
  {
    id: '1',
    type: 'Travel & Transport',
    description: 'Business trip to client location',
    date: '2024-07-20',
    amount: '$1,500.00',
    status: 'Check Done',
    approval: 'Approved'
  },
  {
    id: '2',
    type: 'Meals & Entertainment',
    description: 'Client dinner meeting',
    date: '2024-07-18',
    amount: '$350.00',
    status: 'Check Done',
    approval: 'Approved'
  },
  {
    id: '3',
    type: 'Office Supplies',
    description: 'Monthly office supplies purchase',
    date: '2024-07-15',
    amount: '$280.00',
    status: 'Rejected',
    approval: 'Rejected'
  },
  {
    id: '4',
    type: 'Professional Development',
    description: 'Conference registration and materials',
    date: '2024-07-12',
    amount: '$2,200.00',
    status: 'Check Done',
    approval: 'Approved'
  },
  {
    id: '5',
    type: 'Equipment',
    description: 'Laptop and accessories for remote work',
    date: '2024-07-10',
    amount: '$1,800.00',
    status: 'Check Done',
    approval: 'Approved'
  },
  {
    id: '6',
    type: 'Marketing',
    description: 'Promotional materials and advertising',
    date: '2024-07-08',
    amount: '$950.00',
    status: 'Rejected',
    approval: 'Rejected'
  },
  {
    id: '7',
    type: 'Utilities',
    description: 'Internet and phone reimbursement',
    date: '2024-07-05',
    amount: '$120.00',
    status: 'Check Done',
    approval: 'Approved'
  },
  {
    id: '8',
    type: 'Training',
    description: 'Online course subscription',
    date: '2024-07-03',
    amount: '$299.00',
    status: 'Check Done',
    approval: 'Approved'
  }
];