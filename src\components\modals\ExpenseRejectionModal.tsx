import { useState } from "react";
import RejectionSuccessModal from "./RejectionSuccessModal";
import { IoLayers } from "react-icons/io5";

interface ExpenseItem {
  id: string;
  category: string;
  title: string;
  amount: number;
  date: string;
  description: string;
  uploads: string[];
  markedForRejection: boolean;
}
interface ExpenseRequest {
  id: string;
  employee: string;
  expenseName: string;
  category: string;
  submissionDate: string;
  amount: number;
  isMultiple: boolean;
  expenses?: ExpenseItem[];
  isCompliant: boolean;
  reason?: string;
}
interface ExpenseRejectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  expense: ExpenseRequest | null;
  onSubmitRejection: (data: {
    expenseId: string;
    reasons: string[];
    comment: string;
  }) => void;
  handleUndoRejection: () => void;
}
const categoryImages: Record<string, string> = {
  "Travel & Transportation": "/public/palneIcon.svg",
  "Meals & Entertainment": "/public/burgerIcon.svg",
  Software: "/images/software.svg",
};

const ExpenseRejectionModal: React.FC<ExpenseRejectionModalProps> = ({
  isOpen,
  onClose,
  expense,
  onSubmitRejection,
  handleUndoRejection,
}) => {
  const [selectedReasons, setSelectedReasons] = useState([]);
  const [rejectionComment, setRejectionComment] = useState("");
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);

  const rejectionReasons = [
    "Late Submission",
    "Missing Receipt(s)",
    "Unclear Expense Description",
    "Incorrect/Invalid Receipt(s)",
    "Exceeds Policy Limit (Amount)",
    "Exceeds Policy Limit (Category)",
    "Inappropriate Expense Category",
    "Duplicate Expense",
    "Unreasonable Expense",
    "Requires Further Information",
  ];

  const handleReasonChange = (reason: string) => {
    setSelectedReasons((prev) => {
      if (prev.includes(reason)) {
        return prev.filter((r) => r !== reason);
      } else {
        return [...prev, reason];
      }
    });
  };

  const handleSubmitRejection = () => {
    onSubmitRejection({
      expenseId: expense?.id,
      reasons: selectedReasons,
      comment: rejectionComment,
    });
    setSelectedReasons([]);
    setRejectionComment("");
    // Close the rejection modal first, then open success modal
    onClose();
    setIsSuccessModalOpen(true);
  };

  const handleBackToHome = () => {
    setIsSuccessModalOpen(false);
    // Additional cleanup if needed
  };

  const handleUndoRejectionWrapper = () => {
    setIsSuccessModalOpen(false);
    handleUndoRejection();
  };

  if (!isOpen || !expense) return null;

  const totalAmount =
    expense.expenses?.reduce((sum, exp) => sum + exp.amount, 0) ||
    expense.amount;

  return (
    <>
      {/* Only show rejection modal when success modal is not open */}
      {!isSuccessModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-md  flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-800">
                Expense Rejection
              </h2>
              <button
                onClick={onClose}
                className="text-red-500 hover:text-red-600 font-medium"
              >
                Close
              </button>
            </div>

            <div className="p-6">
              {/* Expense Summary */}
              <div className="bg-red-50 rounded-xl p-2 mb-6">
                <div className="flex middle-align justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-[#F15249] rounded-lg p-2">
                      <IoLayers className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-[#F15249] font-light text-lg">
                      {expense.expenseName}
                    </span>
                  </div>
                  <div className="text-left border border-white rounded-md px-2 py-1">
                    <div className="text-sm text-[#6B7582] mb-1">
                      Total Amount
                    </div>
                    <div className="text-xl font-semibold text-[#5C738A]">
                      $
                      {totalAmount.toLocaleString("en-US", {
                        minimumFractionDigits: 2,
                      })}
                    </div>
                  </div>
                </div>
              </div>

              {expense.expenses && expense.expenses.length > 0 && (
                <div className="mb-6">
                  <div className="text-gray-500 text-sm font-medium mb-3">
                    Rejected Expense
                  </div>

                  {expense.expenses.map((exp) => (
                    <div
                      key={exp.id}
                      className="border-b border-gray-100 last:border-b-0 py-3"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 font-bold mb-2">
                          <span className="font-medium text-[#5D7285] text-sm">
                            {exp.title}
                          </span>

                          <span className="text-[#5C738A] rounded text-sm flex items-center gap-1">
                            {categoryImages[exp.category] && (
                              <img
                                src={categoryImages[exp.category]}
                                alt={exp.category}
                                className="w-4 h-4"
                              />
                            )}
                            {exp.category}
                          </span>

                          <span className="text-gray-500 text-sm">
                            {exp.date}
                          </span>
                        </div>

                        <div className="flex justify-between items-center rounded-xl p-1 px-6 mb-4 bg-[#F2F2F5]">
                          <span className="text-sm font-semibold text-[#5C738A]">
                            ${exp.amount.toFixed(2)}
                          </span>
                        </div>
                      </div>

                      {/* Individual Description */}
                      {exp.description && (
                        <>
                          <p className="text-[#B6BEC5] font-bold text-xs">
                            Description
                          </p>
                          <div className="mt-2 text-gray-600 text-xs leading-relaxed max-h-20 p-2 shadow-custom-desc overflow-y-auto pr-1">
                            <p className="text-[#7F7F7F]">{exp.description}</p>
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Rejection Reasons */}
              <div className="mb-6">
                <h3 className="text-[#5C738A] font-medium mb-4 text-sm">
                  Select Rejection Reason(s)
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {rejectionReasons.map((reason: string) => (
                     <label
                     key={reason}
                     className="flex items-center gap-3 cursor-pointer select-none py-1"
                   >
                     <div className="relative w-5 h-5 flex-shrink-0">
                       <input
                         type="checkbox"
                         checked={selectedReasons.includes(reason)}
                         onChange={() => handleReasonChange(reason)}
                         className="absolute w-full h-full opacity-0 cursor-pointer"
                       />
                       <div className={`w-5 h-5 rounded-sm border-2 flex items-center justify-center transition-all duration-200 ${
                         selectedReasons.includes(reason)
                           ? 'bg-[#5EEAD4] border-[#5EEAD4]'
                           : 'bg-white border-[#D1D5DB] hover:border-[#5EEAD4]'
                       }`}>
                         {selectedReasons.includes(reason) && (
                           <svg
                             className="w-3 h-3 text-white"
                             viewBox="0 0 16 16"
                             fill="currentColor"
                           >
                             <path d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"/>
                           </svg>
                         )}
                       </div>
                     </div>
                     <span className="text-[#6B7280] text-sm font-medium">
                       {reason}
                     </span>
                   </label>
                  ))}
                </div>
              </div>

              {/* Rejection Reason Comment */}
              <div className="mb-6">
                <label className="block text-[#5C738A] font-medium text-sm mb-3">
                  Rejection reason
                </label>
                <textarea
                  value={rejectionComment}
                  onChange={(e) => setRejectionComment(e.target.value)}
                  className="w-full h-32 p-4 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder=""
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  onClick={handleSubmitRejection}
                  className="bg-gray-700 text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-colors font-medium"
                >
                  Submit Rejection
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {/* Rejection Success Modal */}
      <RejectionSuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => setIsSuccessModalOpen(false)}
        onUndoRejection={handleUndoRejectionWrapper}
        onBackToHome={handleBackToHome}
      />
    </>
  );
};
export default ExpenseRejectionModal;
