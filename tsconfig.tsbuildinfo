{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/api/client.ts", "./src/components/buttongroup.tsx", "./src/components/card.tsx", "./src/components/datepicker.tsx", "./src/components/dropdown.tsx", "./src/components/pagination.tsx", "./src/components/statusbadge.tsx", "./src/components/verticaldivider.tsx", "./src/components/approvals/approvalscontainer.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/common/expensemodal.tsx", "./src/components/common/receiptupload.tsx", "./src/components/common/successmodal.tsx", "./src/components/insights/insightscontainer.tsx", "./src/components/layout/authpagelayout.tsx", "./src/components/layout/mainlayout.tsx", "./src/components/layout/profilelayout.tsx", "./src/components/modals/expensetrackermodal.tsx", "./src/components/modals/raisedisputemodal.tsx", "./src/components/modals/spendingmodal.tsx", "./src/components/modals/supportmodal.tsx", "./src/components/modals/trackdisputemodal.tsx", "./src/components/navbar/navbar.tsx", "./src/components/support/myqueries.tsx", "./src/components/support/queryform.tsx", "./src/components/support/faqdata.tsx", "./src/components/tables/disputetable.tsx", "./src/components/tables/expensetable.tsx", "./src/components/ui/errordisplay.tsx", "./src/components/ui/loadingspinner.tsx", "./src/db/expensedata.ts", "./src/hooks/useauth.ts", "./src/hooks/usenavigation.ts", "./src/pages/notifications.tsx", "./src/pages/privacypolicy/privacypolicy.tsx", "./src/pages/accountinformation/accountinformation.tsx", "./src/pages/financeapprovals/financeapprovals.tsx", "./src/pages/home/<USER>", "./src/pages/login/login.tsx", "./src/pages/managerapprovals/managerapprovals.tsx", "./src/pages/myinsights/myinsights.tsx", "./src/pages/myteam/myteam.tsx", "./src/pages/notfound/notfound.tsx", "./src/pages/profile/profilesettings.tsx", "./src/pages/support/supportfaq.tsx", "./src/pages/teaminsights/teaminsights.tsx", "./src/pages/users/users.tsx", "./src/router/router.tsx", "./src/router/routes.tsx", "./src/store/themestore.ts", "./src/types/auth.ts", "./src/types/expensetypes.ts", "./src/types/react-modal-image.d.ts", "./src/utils/auth.ts", "./src/utils/formatters.ts", "./src/utils/helperfunctions.ts", "./src/utils/navigation.tsx", "./src/utils/validations.ts"], "version": "5.8.3"}