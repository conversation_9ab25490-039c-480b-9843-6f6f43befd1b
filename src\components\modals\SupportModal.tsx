import React from 'react';
import {
  FaTimesCircle,
} from "react-icons/fa";
import { useNavigate } from 'react-router-dom';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: "success" | "error";
  title: string;
  message: string;
  icons?: string | null | undefined;
}

const SupportModal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  type,
  title,
  message,
  icons,
}) => {
  const navigate = useNavigate();
  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-md bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fadeIn"
      onClick={handleBackdropClick}
    >
      <div className="bg-white max-w-3xl mx-auto rounded-lg shadow-xl w-full transform animate-slideIn">
        <div className="flex justify-between">
          <div className="p-6 flex-1">
            <div className="items-center mb-4">
            {type === "success" ? (
                <img
                  src={icons ?? ""}
                  alt="Success"
                  className="w-[344px] h-[344px] object-contain m-5 mx-auto"
                />
              ) : (
                <FaTimesCircle className="text-red-500 w-6 h-6 mr-3" />
              )}
              <h1 className="text-lg font-semibold text-center text-gray-900">
                {title}
              </h1>
            </div>
            <p className="text-gray-600 mb-6 text-center">{message}</p>

            <div className="flex justify-center gap-4">
              {type === "success" && (
                <button
                  onClick={() => {
                    onClose();
                    navigate("/profile/support");
                  }}
                  className="dark-accent  text-white rounded m-4 px-12 py-2 cursor-pointer transition-colors duration-200 hover:scale-105"
                >
                  Back to Support
                </button>
              )}
              <button
                onClick={onClose}
                className="bg-gray-300 text-gray-700 rounded px-8 py-2 hover:bg-gray-400 transition-colors duration-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportModal;