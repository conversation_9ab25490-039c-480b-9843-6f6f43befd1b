import React, { useState } from "react";
import {
  <PERSON>r,
  CreditCard,
  MessageSquare,
  HelpCircle,
} from "lucide-react";
import {
    FaChevronDown,
    FaChevronUp,
  } from "react-icons/fa";

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: "all" | "account" | "billing" | "prompting";
}

// FAQ Data
const faqData: FAQItem[] = [
  {
    id: 1,
    question: "Can I edit a generated response?",
    answer:
      "Yes, you can edit generated responses by clicking the edit button next to the response. You can modify the content and regenerate if needed.",
    category: "all",
  },
  {
    id: 2,
    question: "How do I reset my password?",
    answer:
      "You can reset your password by selecting 'Forgot password?' on the login screen. Follow the instructions sent to your email to create a new one. If you need further assistance, contact our support team.",
    category: "account",
  },
  {
    id: 3,
    question: "Can I export my data?",
    answer:
      "Yes, you can export your conversation history and data from your account settings. Go to Privacy & Data section and select 'Export Data'.",
    category: "all",
  },
  {
    id: 4,
    question: "How can I delete my history?",
    answer:
      "You can delete your conversation history by going to Settings > Privacy & Data > Delete History. You can choose to delete individual conversations or all history.",
    category: "all",
  },
  {
    id: 5,
    question: "Are my data secure?",
    answer:
      "Yes, we take data security seriously. All data is encrypted in transit and at rest. We follow industry-standard security practices and regularly audit our systems.",
    category: "all",
  },
  {
    id: 6,
    question: "How do I report an issue?",
    answer:
      "You can report issues through this support form, email us directly, or use the feedback button in the application. Please provide as much detail as possible.",
    category: "all",
  },
  {
    id: 7,
    question: "How do I update my billing information?",
    answer:
      "Go to Account Settings > Billing to update your payment method, billing address, or subscription details. Changes take effect immediately.",
    category: "billing",
  },
  {
    id: 8,
    question: "What payment methods do you accept?",
    answer:
      "We accept all major credit cards, PayPal, and bank transfers for enterprise accounts. All payments are processed securely through our payment partners.",
    category: "billing",
  },
  {
    id: 9,
    question: "How do I write better prompts?",
    answer:
      "For better prompts, be specific and clear about what you want. Provide context, examples, and specify the format you prefer. Break complex tasks into smaller steps.",
    category: "prompting",
  },
  {
    id: 10,
    question: "What are some prompting best practices?",
    answer:
      "Use clear instructions, provide examples, specify output format, use step-by-step reasoning, and iterate on your prompts based on the results you get.",
    category: "prompting",
  },
];

const FAQ: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<
    "all" | "account" | "billing" | "prompting"
  >("all");

  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  const toggleExpanded = (id: number) => {
    setExpandedItems((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]
    );
  };

  const filteredFAQs = faqData.filter(
    (item) => activeCategory === "all" || item.category === activeCategory
  );

  const categories = [
    { id: "all", label: "All", icon: HelpCircle },
    { id: "account", label: "Account", icon: User },
    { id: "billing", label: "Billing", icon: CreditCard },
    { id: "prompting", label: "Prompting", icon: MessageSquare },
  ];

  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold text-[slategray] mb-6">FAQ's</h2>

      {/* Category Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() =>
              setActiveCategory(
                id as "all" | "account" | "billing" | "prompting"
              )
            }
            className={`cursor-pointer w-70 sm:w-30 md:w-40 lg:w-50 text-center  flex items-center justify-center px-4 py-2 rounded-full transition-transform duration-300 hover:scale-105 ${
              activeCategory === id
                ? "bg-teal-500 text-white shadow-lg"
                : "bg-blue-50 text-teal-600 hover:bg-blue-100"
            }`}
          >
            <Icon className="w-4 h-4 mr-2" />
            {label}
          </button>
        ))}
      </div>

      {/* FAQ Items */}
      <div className="space-y-4 max-h-[320px] overflow-y-auto divide-y divide-gray-200 scrollbar-hide">
        {filteredFAQs.map((item) => (
          <div
            key={item.id}
            className=" border-0 rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200"
          >
            <button
              onClick={() => toggleExpanded(item.id)}
              className="w-full px-6 py-4 cursor-pointer text-left flex items-center justify-between bg-[whitesmoke] hover:bg-[whitesmoke] transition-colors duration-200"
            >
              <span className="font-medium text-gray-900">{item.question}</span>
              {expandedItems.includes(item.id) ? (
                <FaChevronUp className="w-5 h-5 text-teal-500" />
              ) : (
                <FaChevronDown className="w-5 h-5 text-teal-500" />
              )}
            </button>

            <div
              className={`transition-all duration-300 ease-in-out transform origin-top ${
                expandedItems.includes(item.id)
                  ? "max-h-96 opacity-100"
                  : "max-h-0 opacity-0"
              } overflow-hidden`}
            >
              <div className="px-6 pb-4 bg-[whitesmoke]">
                <p className="text-[dimgray]">{item.answer}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredFAQs.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <HelpCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No FAQs found for this category.</p>
        </div>
      )}
    </div>
  );
};

export default FAQ;
