import { useRouter } from "@tanstack/react-router";

// Simple navigation using window.location (not recommended for SPA)
export const navigateToRoute = (path: string) => {
  window.location.href = path;
};

export const simpleNavigation = {
  home: () => navigateToRoute("/"),
  login: () => navigateToRoute("/auth/login"),
  insights: () => navigateToRoute("/insights"),
  notifications: () => navigateToRoute("/notifications"),
  finance: () => navigateToRoute("/finance"),
  management: () => navigateToRoute("/management"),
  profile: () => navigateToRoute("/profile"),
  accountInfo: () => navigateToRoute("/profile/account-information"),
  privacyPolicy: () => navigateToRoute("/profile/privacy-policy"),
  support: () => navigateToRoute("/profile/support"),
  unauthorized: () => navigateToRoute("/unauthorized"),
};

// Type-safe navigation hook using proper TanStack Router types
export const useSimpleNavigation = () => {
  const router = useRouter();

  // Use the router's navigate method with proper typing
  const navigate = (to: string) => {
    router.navigate({ to } as { to: string });
  };

  return {
    navigate,
    navigateTo: {
      home: () => navigate("/"),
      login: () => navigate("/auth/login"),
      insights: () => navigate("/insights"),
      notifications: () => navigate("/notifications"),
      finance: () => navigate("/finance"),
      management: () => navigate("/management"),
      profile: () => navigate("/profile"),
      accountInfo: () => navigate("/profile/account-information"),
      privacyPolicy: () => navigate("/profile/privacy-policy"),
      support: () => navigate("/profile/support"),
      unauthorized: () => navigate("/unauthorized"),
    },
  };
};

// Alternative: More type-safe approach using specific route paths
export const useTypeSafeNavigation = () => {
  const router = useRouter();

  return {
    navigateTo: {
      home: () => router.navigate({ to: "/" }),
      login: () => router.navigate({ to: "/auth/login" }),
      insights: () => router.navigate({ to: "/insights" }),
      notifications: () => router.navigate({ to: "/notifications" }),
      finance: () => router.navigate({ to: "/finance" }),
      management: () => router.navigate({ to: "/management" }),
      profile: () => router.navigate({ to: "/profile" }),
      accountInfo: () =>
        router.navigate({ to: "/profile/account-information" }),
      privacyPolicy: () => router.navigate({ to: "/profile/privacy-policy" }),
      support: () => router.navigate({ to: "/profile/support" }),
      unauthorized: () => router.navigate({ to: "/unauthorized" }),
    },
  };
};
