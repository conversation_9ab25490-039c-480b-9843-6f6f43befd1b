import React from "react";

type StatusType = "Pending" | "Approved" | "Rejected";

interface StatusBadgeProps {
  status: StatusType;
  className?: string;
}

const statusConfig: Record<StatusType, { text: string; classes: string }> = {
  Pending: {
    text: "Pending",
    classes: "bg-gray-100 text-xs font-bold text-gray-800 border border-gray-300",
  },
  Approved: {
    text: "Approved",
    classes: "bg-green-100 text-xs font-bold text-green-800 border border-green-300",
  },
  Rejected: {
    text: "Rejected",
    classes: "bg-red-100 text-xs font-bold text-red-800 border border-red-300",
  },
};

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className = "" }) => {
  const config = statusConfig[status];
  if (!config) return null;

  return (
    <span
      className={`inline-block px-3 py-1 text-sm font-medium rounded-full ${config.classes} ${className}`}
    >
      {config.text}
    </span>
  );
};

export default StatusBadge;
