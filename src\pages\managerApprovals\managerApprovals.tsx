import React, { useState } from "react";
import { User, Calendar, DollarSign, FileText } from "lucide-react";
import ExpenseDetailsModal from "../../components/modals/ExpenseDetailsModal";

// Types
export interface ExpenseItem {
  id: string;
  category: string;
  title: string;
  amount: number;
  date: string;
  description: string;
  uploads: string[];
  markedForRejection: boolean;
}

export interface ExpenseRequest {
  id: string;
  employee: string;
  expenseName: string;
  category: string;
  submissionDate: string;
  amount: number;
  isMultiple: boolean;
  expenses?: ExpenseItem[];
  isCompliant: boolean;
  reason?: string;
}

// Mock Data
const mockExpenseRequests: ExpenseRequest[] = [
  {
    id: "1",
    employee: "<PERSON>",
    expenseName: "Travel to client location",
    category: "Multiple Expenses",
    submissionDate: "2024-07-20",
    amount: 4000.0,
    isMultiple: true,
    isCompliant: false,
    reason: "Missing Receipt(s)",
    expenses: [
      {
        id: "1a",
        category: "Meals & Entertainment",
        title: "Business Lunch",
        amount: 75.0,
        date: "2024-03-13",
        description: "Business lunch with client.Business lunch with client.Business lunch with client.Business lunch with client.Business lunch with client.",
        uploads: ["/public/reciept.png", "/public/reciept.png"],
        markedForRejection: false,
      },
      {
        id: "1b",
        category: "Travel & Transportation",
        title: "Business travel to LA",
        amount: 975.0,
        date: "2024-03-13",
        description: "Flight and taxi fares to LA.",
        uploads: [],
        markedForRejection: true,
      },
    ],
  },
  {
    id: "2",
    employee: "Ethan Kim",
    expenseName: "Attend conference",
    category: "Travel & Transportation",
    submissionDate: "2024-08-15",
    amount: 1200.0,
    isMultiple: false,
    isCompliant: true,
  },
  {
    id: "3",
    employee: "Sofia Lee",
    expenseName: "Monthly subscription",
    category: "Multiple Expenses",
    submissionDate: "2024-08-01",
    amount: 250.0,
    isMultiple: true,
    isCompliant: true,
    expenses: [
      {
        id: "3a",
        category: "Software",
        title: "Adobe Creative Suite",
        amount: 150.0,
        date: "2024-08-01",
        description: "Design tool subscription",
        uploads: ["/public/reciept.png"],
        markedForRejection: false,
      },
      {
        id: "3b",
        category: "Software",
        title: "Figma Pro",
        amount: 100.0,
        date: "2024-08-01",
        description: "Collaboration design tool",
        uploads: ["/public/reciept.png"],
        markedForRejection: false,
      },
    ],
  },
  {
    id: "4",
    employee: "Mark Chen",
    expenseName: "Team building event",
    category: "Multiple Expenses",
    submissionDate: "2024-08-20",
    amount: 500.0,
    isMultiple: true,
    isCompliant: true,
  },
];

// Stats Card Component
const statsData = [
  { title: "All Claims", value: 57, change: "+10%", color: "text-blue-600" },
  {
    title: "Approved Claims",
    value: 23,
    change: "+8%",
    color: "text-green-600",
  },
  {
    title: "Pending Approval",
    value: 4,
    change: "-5%",
    color: "text-orange-600",
  },
  { title: "Team Members", value: 7, change: "-", color: "text-purple-600" },
];

const StatsCard: React.FC<{
  title: string;
  value: number;
  change: string;
  color: string;
}> = ({ title, value, change, color }) => (
  <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
    <h3 className="text-gray-500 text-sm font-medium mb-2">{title}</h3>
    <div className="flex items-end justify-between">
      <span className="text-3xl font-bold text-gray-900">{value}</span>
      <span className={`text-sm font-medium ${color}`}>{change}</span>
    </div>
  </div>
);

// Badge for Category
const CategoryBadge: React.FC<{ category: string; isMultiple?: boolean }> = ({
  category,
  isMultiple,
}) => {
  const getIcon = () => {
    if (isMultiple) return <FileText className="w-4 h-4" />;
    if (category.includes("Travel")) return <Calendar className="w-4 h-4" />;
    return <DollarSign className="w-4 h-4" />;
  };

  return (
    <span className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
      {getIcon()}
      {category}
    </span>
  );
};

// Main Component
const ManagerApprovals: React.FC = () => {
  const [selectedExpense, setSelectedExpense] = useState<ExpenseRequest | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expenses, setExpenses] =
    useState<ExpenseRequest[]>(mockExpenseRequests);

  const handleViewExpense = (expense: ExpenseRequest) => {
    setSelectedExpense(expense);
    setIsModalOpen(true);
  };

  const handleApprove = (id: string) => {
    setExpenses((prev) => prev.filter((exp) => exp.id !== id));
    setIsModalOpen(false);
    setSelectedExpense(null);
  };

  const handleReject = (id: string) => {
    setExpenses((prev) => prev.filter((exp) => exp.id !== id));
    setIsModalOpen(false);
    setSelectedExpense(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {statsData.map((stat, index) => (
            <div
              key={stat.title}
              className="animate-slideUp"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <StatsCard {...stat} />
            </div>
          ))}
        </div>

        <div
          className="bg-white rounded-lg shadow-sm border border-gray-200 animate-slideUp"
          style={{ animationDelay: "400ms" }}
        >
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Expense Requests - Manager
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  {[
                    "Employee",
                    "Expense Name",
                    "Category",
                    "Submission Date",
                    "Amount",
                    "Actions",
                  ].map((header) => (
                    <th
                      key={header}
                      className="text-left py-4 px-6 font-medium text-gray-700"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {expenses.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="text-center py-4 text-gray-500">
                      No expenses found.
                    </td>
                  </tr>
                ) : (
                  expenses.map((expense, index) => (
                    <tr
                      key={expense.id || index}
                      className="hover:bg-gray-50 transition-colors animate-slideUp"
                      style={{ animationDelay: `${500 + index * 100}ms` }}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                            <User className="w-4 h-4 text-gray-600" />
                          </div>
                          <span className="font-medium text-gray-900">
                            {expense.employee}
                          </span>
                        </div>
                      </td>
                      <td className="py-4 px-6 text-gray-700">
                        {expense.expenseName}
                      </td>
                      <td className="py-4 px-6">
                        <CategoryBadge
                          category={expense.category}
                          isMultiple={expense.isMultiple}
                        />
                      </td>
                      <td className="py-4 px-6 text-gray-600">
                        {expense.submissionDate}
                      </td>
                      <td className="py-4 px-6 font-semibold text-gray-900">
                        ${expense.amount.toFixed(2)}
                      </td>
                      <td className="py-4 px-6">
                        <button
                          onClick={() => handleViewExpense(expense)}
                          className="bg-[lavender] hover:bg-gray-200 px-7 py-[4px] text-sm text-slategray rounded-full font-medium transition-colors flex items-center gap-2"
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      <ExpenseDetailsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        expense={selectedExpense}
        onApprove={handleApprove}
        onReject={handleReject}
      />
    </div>
  );
};

export default ManagerApprovals;
